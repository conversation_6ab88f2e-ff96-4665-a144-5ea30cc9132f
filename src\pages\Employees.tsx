import { useState, useEffect } from "react"
import { <PERSON>, useNavigate } from "react-router-dom"
import { Plus, Search, Edit, Trash2, Eye, MoreHorizontal, User, Mail, Phone, Calendar, Building, CreditCard, FileText, DollarSign } from "lucide-react"
import { Layout } from "@/components/Layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"
// Add these new helper interfaces above your Employee interface
import { usePermissions } from "@/hooks/usePermissions";
interface SalaryComponentDetail {
  value: number;
  salary_components: {
    name: string;
    component_type: 'earning' | 'deduction';
  };
}

interface ContractDetail {
  id: string;
  start_date: string;
  end_date?: string;
  basic_salary: number;
  status: string;
  probation_period?: number;
  notice_period?: number;
  overtime_allowed?: boolean;
  overtime_rate?: number;
  contract_types: {
    name: string;
    code: string; // Your query fetches this, so we should include it
  };
  employee_salary_components?: SalaryComponentDetail[];
}

interface ContractGroupDetail {
  id: string;
  name: string;
  start_date: string;
  end_date?: string;
  status: string;
  contracts: ContractDetail[];
}

interface Employee {
  id: string
  employee_code: string
  company_email: string
  hire_date: string
  employment_status: string
  onboarding_status: string
  is_active: boolean
  created_at: string
  user_profiles: {
    first_name: string
    last_name: string
    middle_name?: string
    personal_email: string
    phone: string
    date_of_birth?: string
    gender?: string
    address?: string
    city?: string
    state?: string
    pincode?: string
  }
  departments: {
    name: string
    code: string
  } | null
  designations: {
    title: string
    code: string
  } | null
  employee_bank_details?: {
    bank_name: string
    account_holder_name: string
    account_number: string
    ifsc_code: string
    branch_name?: string
    is_primary: boolean
  }[]
  contract_groups?: ContractGroupDetail[]
  employee_documents?: {
    id: string
    document_type: string
    file_url: string
    uploaded_at: string
    is_verified: boolean
  }[]
}

export default function Employees() {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const { toast } = useToast()
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();
  const navigate = useNavigate()


  useEffect(() => {
    fetchEmployees()
  }, [])

  const fetchEmployees = async () => {
    try {
      const { data: employeeData, error } = await supabase
        .from('employees')
        .select(`
          id,
          user_id,
          employee_code,
          company_email,
          hire_date,
          employment_status,
          onboarding_status,
          is_active,
          created_at,
          department_id,
          designation_id
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Get user IDs and fetch profiles separately
      const userIds = employeeData?.map(emp => emp.user_id) || []
      const { data: profiles } = await supabase
        .from('user_profiles')
        .select('id, first_name, last_name, middle_name, personal_email, phone, date_of_birth, gender, address, city, state, pincode')
        .in('id', userIds)

      // Fetch departments
      const departmentIds = employeeData?.map(emp => emp.department_id).filter(Boolean) || []
      const { data: departments } = departmentIds.length > 0 ? await supabase
        .from('departments')
        .select('id, name, code')
        .in('id', departmentIds) : { data: [] }

      // Fetch designations
      const designationIds = employeeData?.map(emp => emp.designation_id).filter(Boolean) || []
      const { data: designations } = designationIds.length > 0 ? await supabase
        .from('designations')
        .select('id, title, code')
        .in('id', designationIds) : { data: [] }

      // Combine data
      const combinedData = employeeData?.map(employee => ({
        ...employee,
        user_profiles: profiles?.find(p => p.id === employee.user_id) || {
          first_name: '',
          last_name: '',
          middle_name: '',
          personal_email: '',
          phone: '',
          date_of_birth: null,
          gender: null,
          address: null,
          city: null,
          state: null,
          pincode: null
        },
        departments: departments?.find(d => d.id === employee.department_id) || null,
        designations: designations?.find(d => d.id === employee.designation_id) || null
      })) || []

      setEmployees(combinedData)
    } catch (error) {
      console.error('Error fetching employees:', error)
      toast({
        title: "Error",
        description: "Failed to fetch employees",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchEmployeeDetails = async (employeeId: string) => {
    try {
      // Get employee basic info
      const { data: employee, error } = await supabase
        .from('employees')
        .select('*')
        .eq('id', employeeId)
        .single()

      if (error) throw error

      // Get user profile
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', employee.user_id)
        .single()

      // Get department
      const { data: department } = employee.department_id ? await supabase
        .from('departments')
        .select('name, code')
        .eq('id', employee.department_id)
        .single() : { data: null }

      // Get designation
      const { data: designation } = employee.designation_id ? await supabase
        .from('designations')
        .select('title, code')
        .eq('id', employee.designation_id)
        .single() : { data: null }

      // Get bank details
      const { data: bankDetails } = await supabase
        .from('employee_bank_details')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('is_deleted', false)

      // Get contract groups with contracts
      const { data: contractGroups } = await supabase
        .from('contract_groups')
        .select(`
          id,
          name,
          start_date,
          end_date,
          status,
          contracts(
            id,
            start_date,
            end_date,
            basic_salary,
            status,
            probation_period,
            notice_period,
            overtime_allowed,
            overtime_rate,
            contract_types(name, code),
            employee_salary_components(
              value,
              salary_components(name, component_type)
            )
          )
        `)
        .eq('employee_id', employeeId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })

      // Get documents
      const { data: documents } = await supabase
        .from('employee_documents')
        .select('id, document_type, file_url, uploaded_at, is_verified')
        .eq('employee_id', employeeId)
        .eq('is_deleted', false)

      return {
        ...employee,
        user_profiles: profile || {
          first_name: '',
          last_name: '',
          personal_email: '',
          phone: '',
        },
        departments: department,
        designations: designation,
        employee_bank_details: bankDetails || [],
        contract_groups: contractGroups || [],
        employee_documents: documents || []
      }
    } catch (error) {
      console.error('Error fetching employee details:', error)
      return null
    }
  }

  const filteredEmployees = employees.filter(employee =>
    employee.user_profiles.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.user_profiles.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.employee_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.company_email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleView = async (employee: Employee) => {
    const fullEmployee = await fetchEmployeeDetails(employee.id)
    if (fullEmployee) {
      setSelectedEmployee(fullEmployee as any)
      setViewDialogOpen(true)
    }
  }

  const handleEdit = (employeeId: string) => {
    navigate(`/employees/edit/${employeeId}`)
  }

  const handleDelete = async (employeeId: string) => {
    if (confirm('Are you sure you want to delete this employee?')) {
      try {
        const { error } = await supabase
          .from('employees')
          .update({ is_deleted: true })
          .eq('id', employeeId)

        if (error) throw error

        toast({
          title: "Success",
          description: "Employee deleted successfully",
        })
        fetchEmployees()
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete employee",
          variant: "destructive",
        })
      }
    }
  }

  const handleToggleStatus = async (employeeId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('employees')
        .update({ is_active: !currentStatus })
        .eq('id', employeeId)

      if (error) throw error

      toast({
        title: "Success",
        description: `Employee ${!currentStatus ? 'activated' : 'deactivated'} successfully`,
      })
      fetchEmployees()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update employee status",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading employees...</div>
      </div>
    )
  }


  if (permissionsLoading || loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }
  
  if (!canAccess('employees')) {
    return (
      <Layout>
        <div className="p-8 text-center text-red-600">
          <h2>Access Denied</h2>
          <p>You do not have permission to view employees.</p>
        </div>
      </Layout>
    );
  }
  return (
    <Layout>
      <div className=" mx-auto space-y-6 p-4 md:p-6">
        <Card className="shadow-md border-0">
          <CardHeader className="pb-4 bg-[#D25D5D] rounded-t-lg">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-white">Employees</h1>
                <p className="text-white/80 mt-1">Manage employee records and information</p>
              </div>
              <div className="flex items-center space-x-4 w-full sm:w-auto">
                <div className="relative w-full sm:w-72">

                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search employees..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-full bg-white/10 border-white/20 text-white placeholder:text-white/60"
                  />
                </div>
                <div className="hidden sm:flex items-center gap-4">
                  <div className="bg-white/10 rounded-md px-4 py-2 flex items-center gap-2 text-white">
                    <div className="text-sm font-medium">Total:</div>
                    <div className="text-sm font-semibold">{employees.length}</div>
                  </div>
                  {hasPermission('employees', 'add') && (
                  <Link to="/employees/add">
                    <Button className="bg-white text-[#000] hover:bg-white/90">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Employee
                    </Button>
                  </Link>
                   )}
                </div>
              </div>
            </div>
            {/* Mobile Add Button */}
            <div className="sm:hidden w-full mt-4">
              <Link to="/employees/add" className="w-full">
                <Button className="w-full bg-white text-[#4A9782] hover:bg-white/90">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Employee
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {/* Mobile Card View */}
            <div className="block md:hidden">
              {filteredEmployees.map((employee) => (
                <div key={employee.id} className="border-b p-4 space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{employee.user_profiles.first_name} {employee.user_profiles.last_name}</h3>
                      <p className="text-sm text-muted-foreground">{employee.company_email}</p>
                      <p className="text-sm text-muted-foreground">Code: {employee.employee_code}</p>
                      <Badge variant="outline" className="mt-1">
                        {employee.onboarding_status}
                      </Badge>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleView(employee)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(employee.id)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDelete(employee.id)} className="text-red-600">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <div className="flex flex-wrap gap-2 items-center">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={employee.is_active}
                        onCheckedChange={() => handleToggleStatus(employee.id, employee.is_active)}
                      />
                      <span className="text-sm">{employee.is_active ? "Active" : "Inactive"}</span>
                    </div>
                    <Badge variant="outline">
                      {employee.employment_status}
                    </Badge>
                  </div>
                  {employee.departments && (
                    <p className="text-sm">Department: {employee.departments.name}</p>
                  )}
                  {employee.designations && (
                    <p className="text-sm">Designation: {employee.designations.title}</p>
                  )}
                </div>
              ))}
              {filteredEmployees.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No employees found
                </div>
              )}
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Employee Code</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Designation</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Onboarding</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEmployees.map((employee) => (
                    <TableRow key={employee.id}>
                      <TableCell className="font-medium">
                        {employee.user_profiles.first_name} {employee.user_profiles.last_name}
                      </TableCell>
                      <TableCell>{employee.employee_code}</TableCell>
                      <TableCell>
                        <div>
                          <div className="text-sm">{employee.company_email}</div>
                          {employee.user_profiles.personal_email && (
                            <div className="text-sm text-muted-foreground">
                              {employee.user_profiles.personal_email}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{employee.departments?.name || '-'}</TableCell>
                      <TableCell>{employee.designations?.title || '-'}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-2 items-center">
                          <div className="flex items-center space-x-2">
                                                  <Switch
                        checked={employee.is_active}
                        onCheckedChange={() => handleToggleStatus(employee.id, employee.is_active)}
                        disabled={!hasPermission('employees', 'edit')}
                      />

                          </div>
                          <Badge variant="outline">
                            {employee.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={employee.onboarding_status === 'approved' ? 'default' : 'secondary'}>
                          {employee.onboarding_status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm" onClick={() => handleView(employee)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          {hasPermission('employees', 'edit') && (
                          <Button variant="ghost" size="sm" onClick={() => handleEdit(employee.id)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                          {hasPermission('employees', 'delete') && (
                          <Button variant="ghost" size="sm" onClick={() => handleDelete(employee.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredEmployees.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        No employees found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* View Employee Dialog */}
        <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Employee Details
              </DialogTitle>
            </DialogHeader>
            {selectedEmployee && (
              <Tabs defaultValue="personal" className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="personal">Personal</TabsTrigger>
                  <TabsTrigger value="work">Work</TabsTrigger>
                  <TabsTrigger value="contracts">Contracts</TabsTrigger>
                   <TabsTrigger value="banking">Banking</TabsTrigger>
                   <TabsTrigger value="documents">Documents</TabsTrigger>
                </TabsList>

                <TabsContent value="personal" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Personal Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">Full Name</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.user_profiles.first_name} {selectedEmployee.user_profiles.middle_name} {selectedEmployee.user_profiles.last_name}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Date of Birth</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.user_profiles.date_of_birth ? format(new Date(selectedEmployee.user_profiles.date_of_birth), 'PPP') : '-'}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Gender</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.user_profiles.gender || '-'}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Phone</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.user_profiles.phone || '-'}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Personal Email</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.user_profiles.personal_email || '-'}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Address</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.user_profiles.address || '-'}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">City</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.user_profiles.city || '-'}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">State</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.user_profiles.state || '-'}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Pincode</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.user_profiles.pincode || '-'}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="work" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Building className="h-4 w-4" />
                        Work Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">Employee Code</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.employee_code}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Company Email</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.company_email}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Department</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.departments?.name} ({selectedEmployee.departments?.code})
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Designation</label>
                          <p className="text-sm text-muted-foreground">
                            {selectedEmployee.designations?.title} ({selectedEmployee.designations?.code})
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Hire Date</label>
                          <p className="text-sm text-muted-foreground">
                            {format(new Date(selectedEmployee.hire_date), 'PPP')}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Employment Status</label>
                          <Badge variant="outline">
                            {selectedEmployee.employment_status}
                          </Badge>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Onboarding Status</label>
                          <Badge variant={selectedEmployee.onboarding_status === 'approved' ? 'default' : 'secondary'}>
                            {selectedEmployee.onboarding_status}
                          </Badge>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Account Status</label>
                          <Badge variant={selectedEmployee.is_active ? "default" : "secondary"}>
                            {selectedEmployee.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="contracts" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Contract Groups & Contracts
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {selectedEmployee.contract_groups && selectedEmployee.contract_groups.length > 0 ? (
                        <div className="space-y-6">
                          {selectedEmployee.contract_groups.map((group) => (
                            <div key={group.id} className="border-2 border-gray-200 rounded-lg p-4 bg-gray-50">
                              {/* Contract Group Header */}
                              <div className="mb-4 pb-3 border-b border-gray-300">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <h3 className="text-lg font-semibold text-gray-900">{group.name}</h3>
                                    <p className="text-sm text-gray-600">
                                      Group Period: {format(new Date(group.start_date), 'PPP')} -
                                      {group.end_date ? format(new Date(group.end_date), 'PPP') : 'Ongoing'}
                                    </p>
                                  </div>
                                  <Badge variant={group.status === 'active' ? 'default' : 'secondary'}>
                                    {group.status}
                                  </Badge>
                                </div>
                              </div>

                              {/* Contracts in Group */}
                              {group.contracts && group.contracts.length > 0 ? (
                                <div className="space-y-4">
                                  <h4 className="font-medium text-gray-800">Contracts ({group.contracts.length})</h4>
                                  {group.contracts.map((contract, index) => (
                                    <div key={contract.id} className="bg-white border rounded-lg p-4 shadow-sm">
                                      <div className="flex items-center justify-between mb-3">
                                        <h5 className="font-medium text-gray-900">Contract #{index + 1}</h5>
                                        <Badge variant={contract.status === 'active' ? 'default' : 'secondary'}>
                                          {contract.status}
                                        </Badge>
                                      </div>

                                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <div>
                                          <label className="text-sm font-medium text-gray-700">Contract Type</label>
                                          <p className="text-sm text-gray-600">
                                            {contract.contract_types?.name} ({contract.contract_types?.code})
                                          </p>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium text-gray-700">Start Date</label>
                                          <p className="text-sm text-gray-600">
                                            {format(new Date(contract.start_date), 'PPP')}
                                          </p>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium text-gray-700">End Date</label>
                                          <p className="text-sm text-gray-600">
                                            {contract.end_date ? format(new Date(contract.end_date), 'PPP') : 'Ongoing'}
                                          </p>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium text-gray-700">Basic Salary</label>
                                          <p className="text-sm font-semibold text-green-600">
                                            ₹{contract.basic_salary.toLocaleString()}
                                          </p>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium text-gray-700">Probation Period</label>
                                          <p className="text-sm text-gray-600">
                                            {contract.probation_period ? `${contract.probation_period} months` : 'N/A'}
                                          </p>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium text-gray-700">Notice Period</label>
                                          <p className="text-sm text-gray-600">
                                            {contract.notice_period ? `${contract.notice_period} days` : 'N/A'}
                                          </p>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium text-gray-700">Overtime</label>
                                          <p className="text-sm text-gray-600">
                                            {contract.overtime_allowed ?
                                              `Allowed (₹${contract.overtime_rate}/hr)` :
                                              'Not Allowed'
                                            }
                                          </p>
                                        </div>
                                      </div>

                                      {/* Salary Components */}
                                      {contract.employee_salary_components && contract.employee_salary_components.length > 0 && (
                                        <div className="mt-4 pt-3 border-t border-gray-200">
                                          <label className="text-sm font-medium text-gray-700 block mb-2">Salary Components</label>
                                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                            {contract.employee_salary_components.map((sc, scIndex) => (
                                              <div key={scIndex} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                                                <span className="text-sm text-gray-700">
                                                  {sc.salary_components.name}
                                                </span>
                                                <div className="flex items-center gap-2">
                                                  <span className="text-sm font-medium">
                                                    ₹{sc.value.toLocaleString()}
                                                  </span>
                                                  <Badge
                                                    variant={sc.salary_components.component_type === 'earning' ? 'default' : 'destructive'}
                                                    className="text-xs"
                                                  >
                                                    {sc.salary_components.component_type}
                                                  </Badge>
                                                </div>
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <p className="text-center text-gray-500 py-4">No contracts in this group</p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-center text-muted-foreground py-8">No contract groups found</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="banking" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4" />
                        Banking Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {selectedEmployee.employee_bank_details && selectedEmployee.employee_bank_details.length > 0 ? (
                        <div className="space-y-4">
                          {selectedEmployee.employee_bank_details.map((bank, index) => (
                            <div key={index} className="border rounded-lg p-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Bank Name</label>
                                  <p className="text-sm text-muted-foreground">
                                    {bank.bank_name}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Account Holder</label>
                                  <p className="text-sm text-muted-foreground">
                                    {bank.account_holder_name}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Account Number</label>
                                  <p className="text-sm text-muted-foreground font-mono">
                                    {bank.account_number}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">IFSC Code</label>
                                  <p className="text-sm text-muted-foreground font-mono">
                                    {bank.ifsc_code}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Branch</label>
                                  <p className="text-sm text-muted-foreground">
                                    {bank.branch_name || '-'}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Account Type</label>
                                  <Badge variant={bank.is_primary ? 'default' : 'secondary'}>
                                    {bank.is_primary ? 'Primary' : 'Secondary'}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-center text-muted-foreground py-8">No banking details found</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="documents" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Documents
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {selectedEmployee.employee_documents && selectedEmployee.employee_documents.length > 0 ? (
                        <div className="space-y-4">
                          {selectedEmployee.employee_documents.map((doc) => (
                            <div key={doc.id} className="flex items-center justify-between border rounded-lg p-4">
                              <div>
                                <p className="font-medium">{doc.document_type}</p>
                                <p className="text-sm text-muted-foreground">
                                  Uploaded: {format(new Date(doc.uploaded_at), 'PPP')}
                                </p>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant={doc.is_verified ? 'default' : 'secondary'}>
                                  {doc.is_verified ? 'Verified' : 'Pending'}
                                </Badge>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => window.open(doc.file_url, '_blank')}
                                >
                                  View
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-center text-muted-foreground py-8">No documents found</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  )
}