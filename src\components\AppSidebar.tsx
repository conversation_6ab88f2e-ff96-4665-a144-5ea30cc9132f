import { 
  Users, 
  UserCog,
  Calendar, 
  BarChart3, 
  FileText, 
  Settings, 
  Home,
  UserPlus,
  Clock,
  DollarSign,
  Award,
  Building2,
  ChevronRight,
  Shield,
  Cog,
  User,
  UserCheck 
} from "lucide-react"
import { NavLink, useLocation, Link } from "react-router-dom"
import { cn } from "@/lib/utils"
import { usePermissions } from "@/hooks/usePermissions"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"

const adminItems = [
{ title: "Dashboard", url: "/", icon: Home, color: "text-blue-500", resource: "dashboard_admin" },
  { title: "Users", url: "/users", icon: UserCog, color: "text-indigo-500", resource: "users" },
  { title: "Employees", url: "/employees", icon: Users, color: "text-green-500", resource: "employees" },
  { title: "Contracts", url: "/contracts", icon: FileText, color: "text-blue-600", resource: "contracts" },
  { title: "Leaves", url: "/leaves", icon: Calendar, color: "text-purple-600", resource: "leaves" },
  { title: "HR Approvals", url: "/hr/approvals", icon: UserPlus, color: "text-purple-500", resource: "hr" },
  { title: "Attendance", url: "/attendance", icon: Clock, color: "text-orange-500", resource: "attendance" },
  { title: "Payroll", url: "/payroll", icon: DollarSign, color: "text-emerald-500", resource: "payroll" },
  { title: "Performance", url: "/performance", icon: Award, color: "text-yellow-500", resource: "performance" },
]

const hrItems = [
   { title: "HR Dashboard", url: "/", icon: Home, color: "text-blue-500", resource: "dashboard_hr" },
  { title: "Employees", url: "/employees", icon: Users, color: "text-green-500", resource: "employees" },
  { title: "Contracts", url: "/contracts", icon: FileText, color: "text-blue-600", resource: "contracts" },
  { title: "Leaves", url: "/leaves", icon: Calendar, color: "text-purple-600", resource: "leaves" },
  { title: "HR Approvals", url: "/hr/approvals", icon: UserPlus, color: "text-purple-500", resource: "hr" },
  { title: "Attendance", url: "/attendance", icon: Clock, color: "text-orange-500", resource: "attendance" },
  { title: "Payroll", url: "/payroll", icon: DollarSign, color: "text-emerald-500", resource: "payroll" },
]

const employeeItems = [
 { title: "My Dashboard", url: "/", icon: Home, color: "text-blue-500", resource: "dashboard_employee" },
  { title: "My Profile", url: "/my-profile", icon: User, color: "text-green-500", resource: "employee" },
  { title: "My Leaves", url: "/leaves", icon: Calendar, color: "text-purple-600", resource: "leaves" },
  { title: "My Attendance", url: "/my-attendance", icon: Clock, color: "text-orange-500", resource: "employee" },
]

const masterItems = [
  { title: "Departments", url: "/masters/departments", icon: Building2, color: "text-blue-500", resource: "masters_departments" },
    { title: "Shifts", url: "/masters/shifts", icon: Clock, color: "text-green-500", resource: "masters_shifts" },
 { title: "Holidays", url: "/masters/holidays", icon: Calendar, color: "text-red-500", resource: "masters_holidays" },
 { title: "Leave Types", url: "/masters/leave-types", icon: FileText, color: "text-purple-500", resource: "masters_leave_types" },
  { title: "Roles", url: "/masters/roles", icon: Users, color: "text-orange-500", resource: "roles_management" },
  { title: "Permissions", url: "/masters/permissions", icon: Shield, color: "text-cyan-500", resource: "permissions_management" },
  { title: "User Permissions", url: "/masters/user-permissions", icon: UserCheck, color: "text-green-500", resource: "user_permissions_management" },
  { title: "Work Weeks", url: "/masters/work-weeks", icon: Calendar, color: "text-indigo-500", resource: "masters_work_weeks" },
   { title: "Contract Types", url: "/masters/contract-types", icon: FileText, color: "text-teal-500", resource: "masters_contract_types" },
  { title: "Salary Components", url: "/masters/salary-components", icon: DollarSign, color: "text-emerald-500", resource: "masters_salary_components" },
]

const managementItems = [
  { title: "Analytics", url: "/analytics", icon: BarChart3, color: "text-indigo-500" },
  { title: "Calendar", url: "/calendar", icon: Calendar, color: "text-red-500" },
  { title: "Reports", url: "/reports", icon: FileText, color: "text-cyan-500" },
  { title: "Settings", url: "/settings", icon: Settings, color: "text-gray-500", resource: "settings_company" }, // Point to one of the settings pages
]

export function AppSidebar() {
  const { state, isMobile, isTablet } = useSidebar()
  const location = useLocation()
  const currentPath = location.pathname
  const isCollapsed = state === "collapsed"
  const { userRole, canAccess } = usePermissions()
  
  // Force collapsed state on tablet in portrait mode
  const shouldForceCollapse = isTablet

  // Get menu items based on role
   const getMainItems = () => {
    switch (userRole) {
      case 'Admin':
        return adminItems.filter(item => item.resource && canAccess(item.resource))
      case 'HR Manager':
        return hrItems.filter(item => item.resource && canAccess(item.resource))
      // 👇 FIX: Simplified the 'Employee' case to match the others
       case 'Employee':
        const items = [...employeeItems]
        // Add additional menu items based on permissions
        if (canAccess('users')) {
          items.push({ title: "Users", url: "/users", icon: UserCog, color: "text-indigo-500", resource: "users" })
        }
        if (canAccess('employees')) {
          items.push({ title: "Employees", url: "/employees", icon: Users, color: "text-green-500", resource: "employees" })
        }
        if (canAccess('contracts')) {
          items.push({ title: "Contracts", url: "/contracts", icon: FileText, color: "text-blue-600", resource: "contracts" })
        }

        if (canAccess('hr')) {
          items.push({ title: "HR Approvals", url: "/hr/approvals", icon: UserPlus, color: "text-purple-500", resource: "hr" })
        }
        if (canAccess('attendance')) {
          items.push({ title: "Attendance", url: "/attendance", icon: Clock, color: "text-orange-500", resource: "attendance" })
        }
        if (canAccess('payroll')) {
          items.push({ title: "Payroll", url: "/payroll", icon: DollarSign, color: "text-emerald-500", resource: "payroll" })
        }
        return items.filter(item => canAccess(item.resource))
      default:
        return employeeItems
    }
  }

  const mainItems = getMainItems()
  const showMasters = masterItems.some(item => item.resource && canAccess(item.resource)); // Extract module from URL
  const showManagement = managementItems.some(item => canAccess(item.url.split('/')[1]))

  const isActive = (path: string) => currentPath === path
  
  const getNavCls = (path: string) => {
    const active = isActive(path)
    return cn(
      "group flex items-center rounded-lg text-sm font-medium transition-all duration-200 ease-in-out",
      isCollapsed ? "justify-center px-2 py-2.5" : "gap-3 px-3 py-2.5",
      active
        ? "bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-md"
        : "text-gray-600 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100"
    )
  }

  return (
    <Sidebar 
      collapsible="icon" 
      className="border-r border-gray-200 dark:border-gray-800"
      data-force-collapsed={shouldForceCollapse ? "true" : undefined}
    >
      {/* Header */}
      <div className={cn(
        "border-b border-gray-200 dark:border-gray-800",
        isCollapsed ? "p-4" : "p-6"
      )}>
        <div className={cn(
          "flex items-center",
          isCollapsed ? "justify-center" : "gap-3"
        )}>
          <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center shadow-lg">
            <Building2 className="w-5 h-5 text-primary-foreground" />
          </div>
          {!isCollapsed && (
            <div>
              <h1 className="font-bold text-xl text-gray-900 dark:text-gray-100">MyLegalDesk</h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">HR Management</p>
            </div>
          )}
        </div>
      </div>

      <SidebarContent className={cn("py-6", isCollapsed ? "px-2" : "px-4")}>
        {/* Main Menu */}
        <SidebarGroup className="mb-8">
          {!isCollapsed && (
            <SidebarGroupLabel className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
              Main Menu
            </SidebarGroupLabel>
          )}
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {mainItems.map((item) => {
                const active = isActive(item.url)
                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <NavLink to={item.url} end className={getNavCls(item.url)}>
                        {isCollapsed ? (
                          <item.icon className={cn(
                            "w-5 h-5 transition-colors",
                            active ? "text-white" : item.color
                          )} />
                        ) : (
                          <>
                            <div className={cn(
                              "flex items-center justify-center w-8 h-8 rounded-lg transition-colors",
                              active ? "bg-white/20" : "bg-gray-100 dark:bg-gray-800"
                            )}>
                              <item.icon className={cn(
                                "w-4 h-4 transition-colors",
                                active ? "text-white" : item.color
                              )} />
                            </div>
                            <span className="flex-1">{item.title}</span>
                            {active && (
                              <ChevronRight className="w-4 h-4 text-white/80" />
                            )}
                          </>
                        )}
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Masters - Only for Admin and HR */}
        {showMasters && (
          <SidebarGroup className="mb-8">
            {!isCollapsed && (
              <SidebarGroupLabel className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
                Masters
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {masterItems.filter(item => item.resource && canAccess(item.resource)).map((item) => {
                  const active = isActive(item.url)
                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild>
                        <NavLink to={item.url} end className={getNavCls(item.url)}>
                          {isCollapsed ? (
                            <item.icon className={cn(
                              "w-5 h-5 transition-colors",
                              active ? "text-white" : item.color
                            )} />
                          ) : (
                            <>
                              <div className={cn(
                                "flex items-center justify-center w-8 h-8 rounded-lg transition-colors",
                                active ? "bg-white/20" : "bg-gray-100 dark:bg-gray-800"
                              )}>
                                <item.icon className={cn(
                                  "w-4 h-4 transition-colors",
                                  active ? "text-white" : item.color
                                )} />
                              </div>
                              <span className="flex-1">{item.title}</span>
                              {active && (
                                <ChevronRight className="w-4 h-4 text-white/80" />
                              )}
                            </>
                          )}
                        </NavLink>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Management - Only for Admin and HR */}
        {showManagement && (
          <SidebarGroup>
            {!isCollapsed && (
              <SidebarGroupLabel className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
                Management
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {managementItems.map((item) => {
                  const active = isActive(item.url)
                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild>
                        <NavLink to={item.url} end className={getNavCls(item.url)}>
                          {isCollapsed ? (
                            <item.icon className={cn(
                              "w-5 h-5 transition-colors",
                              active ? "text-white" : item.color
                            )} />
                          ) : (
                            <>
                              <div className={cn(
                                "flex items-center justify-center w-8 h-8 rounded-lg transition-colors",
                                active ? "bg-white/20" : "bg-gray-100 dark:bg-gray-800"
                              )}>
                                <item.icon className={cn(
                                  "w-4 h-4 transition-colors",
                                  active ? "text-white" : item.color
                                )} />
                              </div>
                              <span className="flex-1">{item.title}</span>
                              {active && (
                                <ChevronRight className="w-4 w-4 text-white/80" />
                              )}
                            </>
                          )}
                        </NavLink>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )
                })}
              </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup>
        )}

      {/* Settings Section - Only for Admin */}
      {userRole === 'Admin' && (
        <SidebarGroup>
          <SidebarGroupLabel>Settings</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {canAccess('settings_company') && (
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/settings/company">
                    <Building2 className="w-4 h-4" />
                    <span>Company Settings</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              )}
              {canAccess('settings_smtp') && (
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/settings/smtp">
                    <Cog className="w-4 h-4" />
                    <span>SMTP Settings</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      )}
    </SidebarContent>
  </Sidebar>
  )
}