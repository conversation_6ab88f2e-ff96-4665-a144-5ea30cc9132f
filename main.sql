create table public.activity_logs (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  action character varying(255) not null,
  module character varying(100) not null,
  description text null,
  metadata jsonb null,
  ip_address inet null,
  user_agent text null,
  timestamp timestamp with time zone null default CURRENT_TIMESTAMP,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint activity_logs_pkey primary key (id),
  constraint activity_logs_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint activity_logs_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint activity_logs_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_activity_logs_user on public.activity_logs using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_activity_logs_module on public.activity_logs using btree (module) TABLESPACE pg_default;

create index IF not exists idx_activity_logs_timestamp on public.activity_logs using btree ("timestamp") TABLESPACE pg_default;

create trigger update_activity_logs_updated_at BEFORE
update on activity_logs for EACH row
execute FUNCTION update_updated_at_column ();



create table public.approval_settings (
  id uuid not null default gen_random_uuid (),
  module character varying(100) not null,
  approval_levels integer null default 1,
  auto_approve_limit numeric(15, 2) null,
  settings jsonb null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint approval_settings_pkey primary key (id),
  constraint approval_settings_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint approval_settings_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_approval_levels check ((approval_levels > 0)),
  constraint positive_auto_approve_limit check (
    (
      (auto_approve_limit is null)
      or (auto_approve_limit >= (0)::numeric)
    )
  )
) TABLESPACE pg_default;

create trigger update_approval_settings_updated_at BEFORE
update on approval_settings for EACH row
execute FUNCTION update_updated_at_column ();


create table public.attendance (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  date date not null,
  status public.attendance_status null default 'present'::attendance_status,
  punch_in_time timestamp with time zone null,
  punch_out_time timestamp with time zone null,
  total_hours numeric(5, 2) null,
  break_hours numeric(5, 2) null default 0,
  overtime_hours numeric(5, 2) null default 0,
  half_day_type public.half_day_type null,
  remarks text null,
  verified_by uuid null,
  verified_at timestamp with time zone null,
  is_verified boolean null default false,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint attendance_pkey primary key (id),
  constraint attendance_employee_id_date_key unique (employee_id, date),
  constraint attendance_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint attendance_verified_by_fkey foreign KEY (verified_by) references auth.users (id),
  constraint attendance_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint attendance_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint positive_hours check (
    (
      (
        (total_hours is null)
        or (total_hours >= (0)::numeric)
      )
      and (break_hours >= (0)::numeric)
      and (overtime_hours >= (0)::numeric)
    )
  ),
  constraint valid_punch_times check (
    (
      (punch_out_time is null)
      or (punch_out_time > punch_in_time)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_attendance_employee on public.attendance using btree (employee_id) TABLESPACE pg_default;

create index IF not exists idx_attendance_date on public.attendance using btree (date) TABLESPACE pg_default;

create index IF not exists idx_attendance_status on public.attendance using btree (status, is_active, is_deleted) TABLESPACE pg_default;

create trigger update_attendance_updated_at BEFORE
update on attendance for EACH row
execute FUNCTION update_updated_at_column ();



create table public.attendance_records (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  attendance_date date not null,
  check_in timestamp with time zone null,
  check_out timestamp with time zone null,
  break_start timestamp with time zone null,
  break_end timestamp with time zone null,
  total_break_duration_minutes numeric null default 0,
  total_hours numeric null,
  overtime_hours numeric null,
  status character varying(50) null,
  is_manual_entry boolean null default false,
  remarks text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint attendance_records_pkey primary key (id),
  constraint unique_employee_attendance_date unique (employee_id, attendance_date),
  constraint attendance_records_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint attendance_records_employee_id_fkey foreign KEY (employee_id) references employees (id),
  constraint attendance_records_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint attendance_records_status_check check (
    (
      (status)::text = any (
        (
          array[
            'present'::character varying,
            'absent'::character varying,
            'half_day'::character varying,
            'holiday'::character varying,
            'leave'::character varying
          ]
        )::text[]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_attendance_records_employee_date on public.attendance_records using btree (employee_id, attendance_date) TABLESPACE pg_default;

create trigger update_attendance_records_updated_at BEFORE
update on attendance_records for EACH row
execute FUNCTION update_updated_at_column ();




create table public.audit_logs (
  id uuid not null default gen_random_uuid (),
  table_name character varying(100) not null,
  record_id uuid not null,
  action public.audit_action not null,
  old_values jsonb null,
  new_values jsonb null,
  changed_fields text[] null,
  user_id uuid null,
  ip_address inet null,
  user_agent text null,
  timestamp timestamp with time zone null default CURRENT_TIMESTAMP,
  constraint audit_logs_pkey primary key (id),
  constraint audit_log_table_record_idx unique (table_name, record_id, "timestamp"),
  constraint audit_logs_user_id_fkey foreign KEY (user_id) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_audit_logs_table_record on public.audit_logs using btree (table_name, record_id) TABLESPACE pg_default;

create index IF not exists idx_audit_logs_user on public.audit_logs using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_audit_logs_timestamp on public.audit_logs using btree ("timestamp") TABLESPACE pg_default;



create table public.company_settings (
  id uuid not null default gen_random_uuid (),
  company_name character varying(255) not null,
  address text null,
  city character varying(100) null,
  state character varying(100) null,
  country character varying(100) null,
  postal_code character varying(20) null,
  phone character varying(20) null,
  email character varying(255) null,
  website character varying(255) null,
  logo_url character varying(500) null,
  tax_id character varying(100) null,
  registration_number character varying(100) null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint company_settings_pkey primary key (id),
  constraint company_settings_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint company_settings_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint valid_company_email check (
    (
      (email is null)
      or (
        (email)::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text
      )
    )
  )
) TABLESPACE pg_default;

create trigger update_company_settings_updated_at BEFORE
update on company_settings for EACH row
execute FUNCTION update_updated_at_column ();



create table public.contract_groups (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  name character varying(255) not null,
  description text null,
  start_date date not null,
  end_date date null,
  status public.contract_status null default 'active'::contract_status,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint contract_groups_pkey primary key (id),
  constraint contract_groups_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint contract_groups_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint contract_groups_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint valid_contract_group_dates check (
    (
      (end_date is null)
      or (end_date >= start_date)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_contract_groups_employee on public.contract_groups using btree (employee_id) TABLESPACE pg_default;

create index IF not exists idx_contract_groups_status on public.contract_groups using btree (status, is_active, is_deleted) TABLESPACE pg_default;

create index IF not exists idx_contract_groups_dates on public.contract_groups using btree (start_date, end_date) TABLESPACE pg_default;

create trigger update_contract_groups_updated_at BEFORE
update on contract_groups for EACH row
execute FUNCTION update_updated_at_column ();




create table public.contract_holidays (
  id uuid not null default gen_random_uuid (),
  contract_id uuid not null,
  holiday_id uuid not null,
  is_applicable boolean null default true,
  remarks text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint contract_holidays_pkey primary key (id),
  constraint unique_contract_holiday unique (contract_id, holiday_id),
  constraint contract_holidays_contract_id_fkey foreign KEY (contract_id) references contracts (id) on delete CASCADE,
  constraint contract_holidays_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint contract_holidays_holiday_id_fkey foreign KEY (holiday_id) references holidays (id) on delete CASCADE,
  constraint contract_holidays_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_contract_holidays_contract on public.contract_holidays using btree (contract_id) TABLESPACE pg_default;

create index IF not exists idx_contract_holidays_holiday on public.contract_holidays using btree (holiday_id) TABLESPACE pg_default;

create unique INDEX IF not exists idx_contract_holidays_unique on public.contract_holidays using btree (contract_id, holiday_id) TABLESPACE pg_default
where
  (is_deleted = false);

create trigger update_contract_holidays_updated_at BEFORE
update on contract_holidays for EACH row
execute FUNCTION update_updated_at_column ();

create table public.contract_leaves (
  id uuid not null default gen_random_uuid (),
  contract_id uuid not null,
  leave_type_id uuid not null,
  days_allowed numeric(10, 2) not null,
  carry_forward boolean null default false,
  encashable boolean null default false,
  salary_payable boolean null default true,
  notes text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint contract_leaves_pkey primary key (id),
  constraint unique_contract_leave_type unique (contract_id, leave_type_id),
  constraint contract_leaves_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint contract_leaves_leave_type_id_fkey foreign KEY (leave_type_id) references leave_types (id) on delete CASCADE,
  constraint contract_leaves_contract_id_fkey foreign KEY (contract_id) references contracts (id) on delete CASCADE,
  constraint contract_leaves_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_days_allowed check ((days_allowed > (0)::numeric))
) TABLESPACE pg_default;

create index IF not exists idx_contract_leaves_contract on public.contract_leaves using btree (contract_id) TABLESPACE pg_default;

create index IF not exists idx_contract_leaves_leave_type on public.contract_leaves using btree (leave_type_id) TABLESPACE pg_default;

create unique INDEX IF not exists idx_contract_leaves_unique on public.contract_leaves using btree (contract_id, leave_type_id) TABLESPACE pg_default
where
  (is_deleted = false);

create trigger trigger_sync_leave_balances_on_contract_leaves
after INSERT
or DELETE
or
update on contract_leaves for EACH row
execute FUNCTION trigger_sync_leave_balances ();

create trigger update_contract_leaves_updated_at BEFORE
update on contract_leaves for EACH row
execute FUNCTION update_updated_at_column ();


create table public.contract_revisions (
  id uuid not null default gen_random_uuid (),
  contract_id uuid not null,
  revision_date date not null default CURRENT_DATE,
  effective_date date not null,
  changes jsonb not null,
  reason text null,
  approved_by uuid null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint contract_revisions_pkey primary key (id),
  constraint contract_revisions_approved_by_fkey foreign KEY (approved_by) references auth.users (id),
  constraint contract_revisions_contract_id_fkey foreign KEY (contract_id) references contracts (id) on delete CASCADE,
  constraint contract_revisions_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint contract_revisions_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint valid_revision_dates check ((effective_date >= revision_date))
) TABLESPACE pg_default;

create index IF not exists idx_contract_revisions_contract on public.contract_revisions using btree (contract_id) TABLESPACE pg_default;

create index IF not exists idx_contract_revisions_dates on public.contract_revisions using btree (revision_date, effective_date) TABLESPACE pg_default;

create trigger update_contract_revisions_updated_at BEFORE
update on contract_revisions for EACH row
execute FUNCTION update_updated_at_column ();



create table public.contract_templates (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  contract_type_id uuid not null,
  content text not null,
  variables jsonb null,
  version integer null default 1,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint contract_templates_pkey primary key (id),
  constraint contract_templates_contract_type_id_fkey foreign KEY (contract_type_id) references contract_types (id) on delete CASCADE,
  constraint contract_templates_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint contract_templates_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_version check ((version > 0))
) TABLESPACE pg_default;

create index IF not exists idx_contract_templates_contract_type on public.contract_templates using btree (contract_type_id) TABLESPACE pg_default;

create index IF not exists idx_contract_templates_version on public.contract_templates using btree (version, is_active, is_deleted) TABLESPACE pg_default;

create trigger update_contract_templates_updated_at BEFORE
update on contract_templates for EACH row
execute FUNCTION update_updated_at_column ();



create table public.contract_type_required_documents (
  id uuid not null default gen_random_uuid (),
  contract_type_id uuid not null,
  document_type character varying(50) not null,
  is_mandatory boolean null default true,
  remarks text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint contract_type_required_documents_pkey primary key (id),
  constraint unique_contract_type_document unique (contract_type_id, document_type),
  constraint contract_type_required_documents_contract_type_id_fkey foreign KEY (contract_type_id) references contract_types (id) on delete CASCADE,
  constraint contract_type_required_documents_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint contract_type_required_documents_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_contract_type_required_documents_contract_type on public.contract_type_required_documents using btree (contract_type_id) TABLESPACE pg_default;

create index IF not exists idx_contract_type_required_documents_type on public.contract_type_required_documents using btree (document_type) TABLESPACE pg_default;

create unique INDEX IF not exists idx_contract_type_documents_unique on public.contract_type_required_documents using btree (contract_type_id, document_type) TABLESPACE pg_default
where
  (is_deleted = false);

create trigger update_contract_type_required_documents_updated_at BEFORE
update on contract_type_required_documents for EACH row
execute FUNCTION update_updated_at_column ();


create table public.contract_types (
  id uuid not null default gen_random_uuid (),
  name character varying(100) not null,
  code character varying(20) not null,
  description text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint contract_types_pkey primary key (id),
  constraint contract_types_code_key unique (code),
  constraint contract_types_name_key unique (name),
  constraint contract_types_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint contract_types_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_contract_types_code on public.contract_types using btree (code) TABLESPACE pg_default;

create trigger update_contract_types_updated_at BEFORE
update on contract_types for EACH row
execute FUNCTION update_updated_at_column ();



create table public.contracts (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  contract_group_id uuid null,
  contract_type_id uuid not null,
  contract_template_id uuid null,
  start_date date not null,
  end_date date null,
  basic_salary numeric(15, 2) not null,
  work_week_id uuid null,
  overtime_allowed boolean null default false,
  overtime_rate numeric(5, 2) null,
  probation_period integer null,
  notice_period integer null,
  status public.contract_status null default 'draft'::contract_status,
  version integer not null default 1,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  parent_contract_id uuid null,
  revision_reason text null,
  constraint contracts_pkey primary key (id),
  constraint contracts_contract_template_id_fkey foreign KEY (contract_template_id) references contract_templates (id) on delete set null,
  constraint contracts_contract_type_id_fkey foreign KEY (contract_type_id) references contract_types (id) on delete RESTRICT,
  constraint contracts_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint contracts_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint contracts_parent_contract_id_fkey foreign KEY (parent_contract_id) references contracts (id) on delete set null,
  constraint contracts_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint contracts_work_week_id_fkey foreign KEY (work_week_id) references work_weeks (id) on delete set null,
  constraint contracts_contract_group_id_fkey foreign KEY (contract_group_id) references contract_groups (id) on delete set null,
  constraint positive_basic_salary check ((basic_salary > (0)::numeric)),
  constraint positive_notice_period check (
    (
      (notice_period is null)
      or (notice_period > 0)
    )
  ),
  constraint positive_overtime_rate check (
    (
      (overtime_rate is null)
      or (overtime_rate > (0)::numeric)
    )
  ),
  constraint positive_probation_period check (
    (
      (probation_period is null)
      or (probation_period > 0)
    )
  ),
  constraint positive_version check ((version > 0)),
  constraint valid_contract_dates check (
    (
      (end_date is null)
      or (end_date >= start_date)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_contracts_employee on public.contracts using btree (employee_id) TABLESPACE pg_default;

create index IF not exists idx_contracts_contract_group on public.contracts using btree (contract_group_id) TABLESPACE pg_default;

create index IF not exists idx_contracts_contract_type on public.contracts using btree (contract_type_id) TABLESPACE pg_default;

create index IF not exists idx_contracts_status on public.contracts using btree (status, is_active, is_deleted) TABLESPACE pg_default;

create index IF not exists idx_contracts_dates on public.contracts using btree (start_date, end_date) TABLESPACE pg_default;

create index IF not exists idx_contracts_parent on public.contracts using btree (parent_contract_id) TABLESPACE pg_default;

create index IF not exists idx_contracts_version on public.contracts using btree (employee_id, version) TABLESPACE pg_default;

create trigger audit_contracts
after INSERT
or DELETE
or
update on contracts for EACH row
execute FUNCTION create_audit_log ();

create trigger update_contracts_updated_at BEFORE
update on contracts for EACH row
execute FUNCTION update_updated_at_column ();



create table public.dashboard_widgets (
  id uuid not null default gen_random_uuid (),
  dashboard_id uuid not null,
  widget_type character varying(50) not null,
  title character varying(255) not null,
  configuration jsonb not null,
  position_x integer not null default 0,
  position_y integer not null default 0,
  width integer not null default 4,
  height integer not null default 3,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint dashboard_widgets_pkey primary key (id),
  constraint dashboard_widgets_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint dashboard_widgets_dashboard_id_fkey foreign KEY (dashboard_id) references dashboards (id) on delete CASCADE,
  constraint dashboard_widgets_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_dimensions check (
    (
      (width > 0)
      and (height > 0)
    )
  ),
  constraint positive_position check (
    (
      (position_x >= 0)
      and (position_y >= 0)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_dashboard_widgets_dashboard on public.dashboard_widgets using btree (dashboard_id) TABLESPACE pg_default;

create trigger update_dashboard_widgets_updated_at BEFORE
update on dashboard_widgets for EACH row
execute FUNCTION update_updated_at_column ();


create table public.dashboards (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  description text null,
  layout jsonb not null,
  is_default boolean null default false,
  role_id uuid null,
  user_id uuid null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint dashboards_pkey primary key (id),
  constraint dashboards_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint dashboards_role_id_fkey foreign KEY (role_id) references roles (id) on delete set null,
  constraint dashboards_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint dashboards_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE,
  constraint dashboard_assignment check (
    (
      (
        (role_id is not null)
        and (user_id is null)
      )
      or (
        (role_id is null)
        and (user_id is not null)
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_dashboards_role on public.dashboards using btree (role_id) TABLESPACE pg_default
where
  (role_id is not null);

create index IF not exists idx_dashboards_user on public.dashboards using btree (user_id) TABLESPACE pg_default
where
  (user_id is not null);

create trigger update_dashboards_updated_at BEFORE
update on dashboards for EACH row
execute FUNCTION update_updated_at_column ();




create table public.data_exports (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  export_type character varying(100) not null,
  table_name character varying(100) not null,
  filters jsonb null,
  format public.report_format null default 'csv'::report_format,
  status character varying(50) null default 'pending'::character varying,
  file_url character varying(500) null,
  record_count integer null,
  file_size_bytes bigint null,
  expires_at timestamp with time zone null,
  downloaded_at timestamp with time zone null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint data_exports_pkey primary key (id),
  constraint data_exports_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint data_exports_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint data_exports_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE,
  constraint positive_file_size check (
    (
      (file_size_bytes is null)
      or (file_size_bytes >= 0)
    )
  ),
  constraint positive_record_count check (
    (
      (record_count is null)
      or (record_count >= 0)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_data_exports_user on public.data_exports using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_data_exports_status on public.data_exports using btree (status) TABLESPACE pg_default;

create index IF not exists idx_data_exports_expires on public.data_exports using btree (expires_at) TABLESPACE pg_default;

create trigger update_data_exports_updated_at BEFORE
update on data_exports for EACH row
execute FUNCTION update_updated_at_column ();




create table public.designations (
  id uuid not null default gen_random_uuid (),
  title character varying(255) not null,
  code character varying(50) null,
  department_id uuid null,
  description text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint designations_pkey primary key (id),
  constraint designations_code_key unique (code),
  constraint designations_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint designations_department_id_fkey foreign KEY (department_id) references departments (id) on delete set null,
  constraint designations_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_designations_department on public.designations using btree (department_id) TABLESPACE pg_default;

create index IF not exists idx_designations_code on public.designations using btree (code) TABLESPACE pg_default
where
  (code is not null);

create trigger update_designations_updated_at BEFORE
update on designations for EACH row
execute FUNCTION update_updated_at_column ();



create table public.email_templates (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  subject character varying(500) not null,
  body text not null,
  template_type public.template_type null,
  variables jsonb null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint email_templates_pkey primary key (id),
  constraint email_templates_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint email_templates_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create trigger update_email_templates_updated_at BEFORE
update on email_templates for EACH row
execute FUNCTION update_updated_at_column ();



create table public.employee_bank_details (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  bank_name character varying(100) not null,
  account_holder_name character varying(255) not null,
  account_number character varying(100) not null,
  ifsc_code character varying(20) not null,
  branch_name character varying(100) null,
  is_primary boolean null default true,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint employee_bank_details_pkey primary key (id),
  constraint unique_employee_bank unique (employee_id, account_number),
  constraint employee_bank_details_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint employee_bank_details_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint employee_bank_details_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint valid_ifsc_code check (
    (
      (ifsc_code)::text ~* '^[A-Z]{4}[0][A-Z0-9]{6}$'::text
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_employee_bank_details_employee on public.employee_bank_details using btree (employee_id) TABLESPACE pg_default;

create unique INDEX IF not exists idx_employee_bank_details_primary on public.employee_bank_details using btree (employee_id) TABLESPACE pg_default
where
  (
    (is_primary = true)
    and (is_deleted = false)
  );

create trigger update_employee_bank_details_updated_at BEFORE
update on employee_bank_details for EACH row
execute FUNCTION update_updated_at_column ();



create table public.employee_documents (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  contract_id uuid null,
  document_type character varying(50) not null,
  file_url text not null,
  uploaded_at timestamp with time zone null default CURRENT_TIMESTAMP,
  verified_by uuid null,
  verified_at timestamp with time zone null,
  is_verified boolean null default false,
  remarks text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  is_mandatory boolean null default true,
  is_skipped boolean null default false,
  skip_reason text null,
  constraint employee_documents_pkey primary key (id),
  constraint employee_documents_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint employee_documents_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint employee_documents_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint employee_documents_verified_by_fkey foreign KEY (verified_by) references auth.users (id),
  constraint fk_employee_documents_contract foreign KEY (contract_id) references contracts (id) on delete set null
) TABLESPACE pg_default;

create index IF not exists idx_employee_documents_employee on public.employee_documents using btree (employee_id) TABLESPACE pg_default;

create index IF not exists idx_employee_documents_type on public.employee_documents using btree (document_type) TABLESPACE pg_default;

create index IF not exists idx_employee_documents_verified on public.employee_documents using btree (is_verified) TABLESPACE pg_default;

create trigger update_employee_documents_updated_at BEFORE
update on employee_documents for EACH row
execute FUNCTION update_updated_at_column ();



create table public.employee_salary_components (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  salary_component_id uuid not null,
  value numeric(15, 2) not null,
  effective_from date not null,
  effective_to date null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  contract_id uuid null,
  constraint employee_salary_components_pkey primary key (id),
  constraint unique_employee_salary_component_active unique (
    employee_id,
    salary_component_id,
    effective_from,
    is_deleted
  ),
  constraint employee_salary_components_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint employee_salary_components_salary_component_id_fkey foreign KEY (salary_component_id) references salary_components (id) on delete CASCADE,
  constraint employee_salary_components_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint employee_salary_components_contract_id_fkey foreign KEY (contract_id) references contracts (id) on delete set null,
  constraint employee_salary_components_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint positive_salary_value check ((value >= (0)::numeric)),
  constraint valid_effective_dates check (
    (
      (effective_to is null)
      or (effective_to >= effective_from)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_employee_salary_components_employee on public.employee_salary_components using btree (employee_id) TABLESPACE pg_default;

create index IF not exists idx_employee_salary_components_component on public.employee_salary_components using btree (salary_component_id) TABLESPACE pg_default;

create index IF not exists idx_employee_salary_components_dates on public.employee_salary_components using btree (effective_from, effective_to) TABLESPACE pg_default;

create index IF not exists idx_employee_salary_components_contract on public.employee_salary_components using btree (contract_id) TABLESPACE pg_default;

create trigger update_employee_salary_components_updated_at BEFORE
update on employee_salary_components for EACH row
execute FUNCTION update_updated_at_column ();



create table public.employee_shifts (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  shift_id uuid not null,
  work_week_id uuid not null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint employee_shifts_pkey primary key (id),
  constraint unique_employee_shift unique (employee_id, shift_id, work_week_id),
  constraint employee_shifts_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint employee_shifts_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint employee_shifts_work_week_id_fkey foreign KEY (work_week_id) references work_weeks (id) on delete CASCADE,
  constraint employee_shifts_shift_id_fkey foreign KEY (shift_id) references shifts (id) on delete CASCADE,
  constraint employee_shifts_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_employee_shifts_employee on public.employee_shifts using btree (employee_id) TABLESPACE pg_default;

create index IF not exists idx_employee_shifts_status on public.employee_shifts using btree (is_active, is_deleted) TABLESPACE pg_default;

create unique INDEX IF not exists idx_employee_shifts_unique_active on public.employee_shifts using btree (employee_id) TABLESPACE pg_default
where
  (
    (is_active = true)
    and (is_deleted = false)
  );

create trigger update_employee_shifts_updated_at BEFORE
update on employee_shifts for EACH row
execute FUNCTION update_updated_at_column ();




create table public.employees (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  employee_code character varying(50) not null,
  company_email character varying(255) not null,
  work_phone character varying(20) null,
  emergency_contact_name character varying(255) null,
  emergency_contact_phone character varying(20) null,
  department_id uuid null,
  designation_id uuid null,
  hire_date date not null,
  employment_status public.employment_status null default 'active'::employment_status,
  onboarding_status public.onboarding_status null default 'pending'::onboarding_status,
  profile_picture_url character varying(500) null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint employees_pkey primary key (id),
  constraint employees_company_email_key unique (company_email),
  constraint employees_employee_code_key unique (employee_code),
  constraint employees_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint employees_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE,
  constraint employees_department_id_fkey foreign KEY (department_id) references departments (id) on delete set null,
  constraint employees_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint employees_designation_id_fkey foreign KEY (designation_id) references designations (id) on delete set null,
  constraint valid_hire_date check ((hire_date <= CURRENT_DATE)),
  constraint valid_company_email check (
    (
      (company_email)::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_employees_user_id on public.employees using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_employees_code on public.employees using btree (employee_code) TABLESPACE pg_default;

create index IF not exists idx_employees_company_email on public.employees using btree (company_email) TABLESPACE pg_default;

create index IF not exists idx_employees_department on public.employees using btree (department_id) TABLESPACE pg_default;

create index IF not exists idx_employees_designation on public.employees using btree (designation_id) TABLESPACE pg_default;

create index IF not exists idx_employees_status on public.employees using btree (employment_status, is_active, is_deleted) TABLESPACE pg_default;

create trigger audit_employees
after INSERT
or DELETE
or
update on employees for EACH row
execute FUNCTION create_audit_log ();

create trigger update_employees_updated_at BEFORE
update on employees for EACH row
execute FUNCTION update_updated_at_column ();



create table public.holidays (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  start_date date not null,
  end_date date not null,
  year integer not null,
  type public.holiday_type null default 'company'::holiday_type,
  description text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint holidays_pkey primary key (id),
  constraint holidays_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint holidays_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint valid_holiday_dates check ((end_date >= start_date)),
  constraint valid_holiday_year check (
    (
      (year > 1900)
      and (year < 3000)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_holidays_date_range on public.holidays using btree (start_date, end_date) TABLESPACE pg_default;

create index IF not exists idx_holidays_year on public.holidays using btree (year) TABLESPACE pg_default;

create trigger update_holidays_updated_at BEFORE
update on holidays for EACH row
execute FUNCTION update_updated_at_column ();


create table public.leave_accrual_rules (
  id uuid not null default gen_random_uuid (),
  leave_type_id uuid not null,
  rule_type character varying(100) null,
  accrual_value numeric(10, 2) not null,
  frequency_days integer null,
  frequency_months integer null,
  min_attendance_required integer null,
  min_working_days integer null,
  max_days_per_year numeric(10, 2) null,
  applicable_after_days integer null default 0,
  apply_to_probation boolean null default true,
  custom_conditions jsonb null,
  notes text null,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  deleted_at timestamp with time zone null,
  constraint leave_accrual_rules_pkey primary key (id),
  constraint leave_accrual_rules_leave_type_id_fkey foreign KEY (leave_type_id) references leave_types (id) on delete CASCADE,
  constraint positive_accrual_value check ((accrual_value > (0)::numeric)),
  constraint positive_frequencies check (
    (
      (
        (frequency_days is null)
        or (frequency_days > 0)
      )
      and (
        (frequency_months is null)
        or (frequency_months > 0)
      )
    )
  ),
  constraint valid_min_values check (
    (
      (
        (min_attendance_required is null)
        or (min_attendance_required >= 0)
      )
      and (
        (min_working_days is null)
        or (min_working_days >= 0)
      )
      and (applicable_after_days >= 0)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_leave_accrual_rules_leave_type on public.leave_accrual_rules using btree (leave_type_id) TABLESPACE pg_default;

create trigger update_leave_accrual_rules_updated_at BEFORE
update on leave_accrual_rules for EACH row
execute FUNCTION update_updated_at_column ();



create table public.leave_approval_workflow (
  id uuid not null default gen_random_uuid (),
  leave_request_id uuid not null,
  level integer not null,
  approver_id uuid not null,
  status public.approval_status null default 'pending'::approval_status,
  action_date timestamp with time zone null,
  comments text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint leave_approval_workflow_pkey primary key (id),
  constraint leave_approval_workflow_leave_request_id_level_key unique (leave_request_id, level),
  constraint leave_approval_workflow_leave_request_id_fkey foreign KEY (leave_request_id) references leave_requests (id) on delete CASCADE,
  constraint leave_approval_workflow_approver_id_fkey foreign KEY (approver_id) references auth.users (id),
  constraint leave_approval_workflow_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint leave_approval_workflow_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_level check ((level > 0))
) TABLESPACE pg_default;

create index IF not exists idx_leave_approval_workflow_request on public.leave_approval_workflow using btree (leave_request_id) TABLESPACE pg_default;

create index IF not exists idx_leave_approval_workflow_approver on public.leave_approval_workflow using btree (approver_id) TABLESPACE pg_default;

create trigger update_leave_approval_workflow_updated_at BEFORE
update on leave_approval_workflow for EACH row
execute FUNCTION update_updated_at_column ();



create table public.leave_balances (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  leave_type_id uuid not null,
  year integer not null,
  allocated_days numeric(10, 2) not null,
  used_days numeric(10, 2) null default 0,
  carried_forward numeric(10, 2) null default 0,
  encashed_days numeric(10, 2) null default 0,
  remaining_days numeric GENERATED ALWAYS as (
    (
      ((allocated_days + carried_forward) - used_days) - encashed_days
    )
  ) STORED (10, 2) null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint leave_balances_pkey primary key (id),
  constraint leave_balances_employee_id_leave_type_id_year_key unique (employee_id, leave_type_id, year),
  constraint leave_balances_leave_type_id_fkey foreign KEY (leave_type_id) references leave_types (id) on delete CASCADE,
  constraint leave_balances_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint leave_balances_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint leave_balances_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint positive_leave_values check (
    (
      (allocated_days >= (0)::numeric)
      and (used_days >= (0)::numeric)
      and (carried_forward >= (0)::numeric)
      and (encashed_days >= (0)::numeric)
    )
  ),
  constraint valid_leave_year check (
    (
      (year > 1900)
      and (year < 3000)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_leave_balances_employee on public.leave_balances using btree (employee_id) TABLESPACE pg_default;

create index IF not exists idx_leave_balances_year on public.leave_balances using btree (year) TABLESPACE pg_default;

create index IF not exists idx_leave_balances_leave_type on public.leave_balances using btree (leave_type_id) TABLESPACE pg_default;

create trigger update_leave_balances_updated_at BEFORE
update on leave_balances for EACH row
execute FUNCTION update_updated_at_column ();



create table public.leave_requests (
  id uuid not null default gen_random_uuid (),
  employee_id uuid not null,
  leave_type_id uuid not null,
  start_date date not null,
  end_date date not null,
  total_days numeric(5, 2) not null,
  half_day_type public.half_day_type null,
  reason text not null,
  status public.leave_status null default 'pending'::leave_status,
  applied_at timestamp with time zone null default CURRENT_TIMESTAMP,
  approved_by uuid null,
  approved_at timestamp with time zone null,
  rejection_reason text null,
  emergency_contact character varying(255) null,
  attachment_url character varying(500) null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint leave_requests_pkey primary key (id),
  constraint leave_requests_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint leave_requests_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint leave_requests_leave_type_id_fkey foreign KEY (leave_type_id) references leave_types (id) on delete RESTRICT,
  constraint leave_requests_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint leave_requests_approved_by_fkey foreign KEY (approved_by) references auth.users (id),
  constraint positive_total_days check ((total_days > (0)::numeric)),
  constraint valid_leave_dates check ((end_date >= start_date))
) TABLESPACE pg_default;

create index IF not exists idx_leave_requests_employee on public.leave_requests using btree (employee_id) TABLESPACE pg_default;

create index IF not exists idx_leave_requests_dates on public.leave_requests using btree (start_date, end_date) TABLESPACE pg_default;

create index IF not exists idx_leave_requests_status on public.leave_requests using btree (status, is_active, is_deleted) TABLESPACE pg_default;

create index IF not exists idx_leave_requests_leave_type on public.leave_requests using btree (leave_type_id) TABLESPACE pg_default;

create trigger audit_leave_requests
after INSERT
or DELETE
or
update on leave_requests for EACH row
execute FUNCTION create_audit_log ();

create trigger update_leave_requests_updated_at BEFORE
update on leave_requests for EACH row
execute FUNCTION update_updated_at_column ();


create table public.leave_types (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  code character varying(50) not null,
  days_allowed integer null,
  carry_forward boolean null default false,
  salary_payable boolean null default true,
  description text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint leave_types_pkey primary key (id),
  constraint leave_types_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint leave_types_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_days_allowed check (
    (
      (days_allowed is null)
      or (days_allowed > 0)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_leave_types_code on public.leave_types using btree (code) TABLESPACE pg_default;

create trigger update_leave_types_updated_at BEFORE
update on leave_types for EACH row
execute FUNCTION update_updated_at_column ();



create table public.notifications (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  title character varying(255) not null,
  message text not null,
  type character varying(50) null default 'info'::character varying,
  read_at timestamp with time zone null,
  action_url character varying(500) null,
  data jsonb null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint notifications_pkey primary key (id),
  constraint notifications_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint notifications_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint notifications_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_notifications_user on public.notifications using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_notifications_read on public.notifications using btree (read_at) TABLESPACE pg_default;

create index IF not exists idx_notifications_created on public.notifications using btree (created_at) TABLESPACE pg_default;

create trigger update_notifications_updated_at BEFORE
update on notifications for EACH row
execute FUNCTION update_updated_at_column ();



create table public.payroll (
  id uuid not null default gen_random_uuid (),
  payroll_period_id uuid not null,
  employee_id uuid not null,
  basic_salary numeric(15, 2) not null,
  gross_salary numeric(15, 2) not null,
  total_deductions numeric(15, 2) not null default 0,
  net_salary numeric(15, 2) not null,
  working_days integer not null,
  present_days integer not null,
  leave_days integer null default 0,
  overtime_hours numeric(5, 2) null default 0,
  overtime_amount numeric(15, 2) null default 0,
  status public.payroll_status null default 'draft'::payroll_status,
  generated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  paid_at timestamp with time zone null,
  payment_method character varying(50) null,
  transaction_id character varying(255) null,
  remarks text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint payroll_pkey primary key (id),
  constraint payroll_payroll_period_id_employee_id_key unique (payroll_period_id, employee_id),
  constraint payroll_payroll_period_id_fkey foreign KEY (payroll_period_id) references payroll_periods (id) on delete CASCADE,
  constraint payroll_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint payroll_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint payroll_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint positive_days check (
    (
      (working_days > 0)
      and (present_days >= 0)
      and (leave_days >= 0)
      and (present_days <= working_days)
    )
  ),
  constraint positive_overtime check (
    (
      (overtime_hours >= (0)::numeric)
      and (overtime_amount >= (0)::numeric)
    )
  ),
  constraint positive_salary_amounts check (
    (
      (basic_salary > (0)::numeric)
      and (gross_salary > (0)::numeric)
      and (total_deductions >= (0)::numeric)
      and (net_salary >= (0)::numeric)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_payroll_period on public.payroll using btree (payroll_period_id) TABLESPACE pg_default;

create index IF not exists idx_payroll_employee on public.payroll using btree (employee_id) TABLESPACE pg_default;

create index IF not exists idx_payroll_status on public.payroll using btree (status, is_active, is_deleted) TABLESPACE pg_default;

create trigger audit_payroll
after INSERT
or DELETE
or
update on payroll for EACH row
execute FUNCTION create_audit_log ();

create trigger update_payroll_updated_at BEFORE
update on payroll for EACH row
execute FUNCTION update_updated_at_column ();



create table public.payroll_components (
  id uuid not null default gen_random_uuid (),
  payroll_id uuid not null,
  salary_component_id uuid not null,
  amount numeric(15, 2) not null,
  calculated_value numeric(15, 2) null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint payroll_components_pkey primary key (id),
  constraint payroll_components_payroll_id_salary_component_id_key unique (payroll_id, salary_component_id),
  constraint payroll_components_payroll_id_fkey foreign KEY (payroll_id) references payroll (id) on delete CASCADE,
  constraint payroll_components_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint payroll_components_salary_component_id_fkey foreign KEY (salary_component_id) references salary_components (id) on delete CASCADE,
  constraint payroll_components_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_payroll_amount check ((amount >= (0)::numeric))
) TABLESPACE pg_default;

create index IF not exists idx_payroll_components_payroll on public.payroll_components using btree (payroll_id) TABLESPACE pg_default;

create index IF not exists idx_payroll_components_component on public.payroll_components using btree (salary_component_id) TABLESPACE pg_default;

create trigger update_payroll_components_updated_at BEFORE
update on payroll_components for EACH row
execute FUNCTION update_updated_at_column ();


create table public.payroll_periods (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  start_date date not null,
  end_date date not null,
  year integer not null,
  month integer not null,
  status public.payroll_status null default 'draft'::payroll_status,
  processed_at timestamp with time zone null,
  processed_by uuid null,
  confirmed_at timestamp with time zone null,
  confirmed_by uuid null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint payroll_periods_pkey primary key (id),
  constraint payroll_periods_year_month_key unique (year, month),
  constraint payroll_periods_processed_by_fkey foreign KEY (processed_by) references auth.users (id),
  constraint payroll_periods_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint payroll_periods_confirmed_by_fkey foreign KEY (confirmed_by) references auth.users (id),
  constraint payroll_periods_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint valid_payroll_dates check ((end_date >= start_date)),
  constraint valid_payroll_month check (
    (
      (month >= 1)
      and (month <= 12)
    )
  ),
  constraint valid_payroll_year check (
    (
      (year > 1900)
      and (year < 3000)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_payroll_periods_year_month on public.payroll_periods using btree (year, month) TABLESPACE pg_default;

create index IF not exists idx_payroll_periods_dates on public.payroll_periods using btree (start_date, end_date) TABLESPACE pg_default;

create trigger update_payroll_periods_updated_at BEFORE
update on payroll_periods for EACH row
execute FUNCTION update_updated_at_column ();


create table public.permissions (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  module character varying(100) not null,
  can_view boolean null default false,
  can_add boolean null default false,
  can_edit boolean null default false,
  can_delete boolean null default false,
  description text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint permissions_pkey primary key (id),
  constraint permissions_name_key unique (name),
  constraint permissions_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint permissions_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_permissions_module on public.permissions using btree (module) TABLESPACE pg_default;

create index IF not exists idx_permissions_security on public.permissions using btree (
  module,
  can_view,
  can_add,
  can_edit,
  can_delete,
  is_active
) TABLESPACE pg_default;

create trigger update_permissions_updated_at BEFORE
update on permissions for EACH row
execute FUNCTION update_updated_at_column ();


create table public.policies (
  id uuid not null default gen_random_uuid (),
  title character varying(255) not null,
  category character varying(100) null,
  content text not null,
  effective_date date null,
  version character varying(50) null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint policies_pkey primary key (id),
  constraint policies_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint policies_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create trigger update_policies_updated_at BEFORE
update on policies for EACH row
execute FUNCTION update_updated_at_column ();




create table public.punch_records (
  id uuid not null default gen_random_uuid (),
  enroll_number integer not null,
  verify_mode character varying null,
  in_out_mode integer null,
  punch_time timestamp with time zone not null,
  is_manual boolean null default false,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint punch_records_pkey primary key (id),
  constraint punch_records_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint punch_records_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_punch_records_enroll_punch_time on public.punch_records using btree (enroll_number, punch_time) TABLESPACE pg_default;

create trigger update_punch_records_updated_at BEFORE
update on punch_records for EACH row
execute FUNCTION update_updated_at_column ();



create table public.report_executions (
  id uuid not null default gen_random_uuid (),
  report_id uuid not null,
  executed_by uuid not null,
  execution_time timestamp with time zone null default CURRENT_TIMESTAMP,
  parameters_used jsonb null,
  status character varying(50) null default 'running'::character varying,
  file_url character varying(500) null,
  error_message text null,
  execution_duration integer null,
  record_count integer null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint report_executions_pkey primary key (id),
  constraint report_executions_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint report_executions_executed_by_fkey foreign KEY (executed_by) references auth.users (id),
  constraint report_executions_report_id_fkey foreign KEY (report_id) references reports (id) on delete CASCADE,
  constraint report_executions_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_duration check (
    (
      (execution_duration is null)
      or (execution_duration >= 0)
    )
  ),
  constraint positive_record_count check (
    (
      (record_count is null)
      or (record_count >= 0)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_report_executions_report on public.report_executions using btree (report_id) TABLESPACE pg_default;

create index IF not exists idx_report_executions_user on public.report_executions using btree (executed_by) TABLESPACE pg_default;

create index IF not exists idx_report_executions_time on public.report_executions using btree (execution_time) TABLESPACE pg_default;

create trigger update_report_executions_updated_at BEFORE
update on report_executions for EACH row
execute FUNCTION update_updated_at_column ();



create table public.reports (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  description text null,
  module character varying(100) not null,
  report_type character varying(50) not null,
  query_template text not null,
  parameters jsonb null,
  format public.report_format null default 'pdf'::report_format,
  is_scheduled boolean null default false,
  schedule_cron character varying(100) null,
  recipients jsonb null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint reports_pkey primary key (id),
  constraint reports_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint reports_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_reports_module on public.reports using btree (module, report_type) TABLESPACE pg_default;

create index IF not exists idx_reports_active on public.reports using btree (is_active, is_deleted) TABLESPACE pg_default;

create trigger update_reports_updated_at BEFORE
update on reports for EACH row
execute FUNCTION update_updated_at_column ();



create table public.role_permissions (
  id uuid not null default gen_random_uuid (),
  role_id uuid not null,
  permission_id uuid not null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint role_permissions_pkey primary key (id),
  constraint role_permissions_role_id_permission_id_key unique (role_id, permission_id),
  constraint role_permissions_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint role_permissions_permission_id_fkey foreign KEY (permission_id) references permissions (id) on delete CASCADE,
  constraint role_permissions_role_id_fkey foreign KEY (role_id) references roles (id) on delete CASCADE,
  constraint role_permissions_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_role_permissions_role on public.role_permissions using btree (role_id) TABLESPACE pg_default;

create index IF not exists idx_role_permissions_permission on public.role_permissions using btree (permission_id) TABLESPACE pg_default;

create trigger update_role_permissions_updated_at BEFORE
update on role_permissions for EACH row
execute FUNCTION update_updated_at_column ();



    create table public.roles (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  description text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint roles_pkey primary key (id),
  constraint roles_name_key unique (name),
  constraint roles_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint roles_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_roles_security on public.roles using btree (name, is_active) TABLESPACE pg_default;

create trigger update_roles_updated_at BEFORE
update on roles for EACH row
execute FUNCTION update_updated_at_column ();




create table public.salary_components (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  code character varying(50) not null,
  component_type public.salary_component_type not null,
  calculation_type public.calculation_type not null,
  default_value numeric(15, 2) null,
  percentage_of character varying(50) null,
  formula text null,
  is_mandatory boolean null default false,
  is_taxable boolean null default true,
  is_provident_fund_applicable boolean null default true,
  is_esi_applicable boolean null default true,
  display_order integer null default 0,
  description text null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint salary_components_pkey primary key (id),
  constraint salary_components_code_key unique (code),
  constraint salary_components_name_key unique (name),
  constraint salary_components_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint salary_components_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_default_value check (
    (
      (default_value is null)
      or (default_value >= (0)::numeric)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_salary_components_code on public.salary_components using btree (code) TABLESPACE pg_default;

create index IF not exists idx_salary_components_type on public.salary_components using btree (component_type, calculation_type) TABLESPACE pg_default;

create trigger update_salary_components_updated_at BEFORE
update on salary_components for EACH row
execute FUNCTION update_updated_at_column ();



create table public.shifts (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  start_time time without time zone not null,
  end_time time without time zone not null,
  break_duration integer null default 0,
  grace_period integer null default 0,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint shifts_pkey primary key (id),
  constraint shifts_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint shifts_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint positive_break_duration check ((break_duration >= 0)),
  constraint positive_grace_period check ((grace_period >= 0)),
  constraint valid_shift_times check (
    (
      (end_time > start_time)
      or (start_time > end_time)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_shifts_active on public.shifts using btree (is_active, is_deleted) TABLESPACE pg_default;

create trigger update_shifts_updated_at BEFORE
update on shifts for EACH row
execute FUNCTION update_updated_at_column ();


create table public.smtp_settings (
  id uuid not null default gen_random_uuid (),
  host character varying(255) not null,
  port integer not null,
  username character varying(255) not null,
  password character varying(255) not null,
  encryption public.encryption_type null,
  from_email character varying(255) not null,
  from_name character varying(255) null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint smtp_settings_pkey primary key (id),
  constraint smtp_settings_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint smtp_settings_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint valid_email_format check (
    (
      (from_email)::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text
    )
  ),
  constraint valid_port check (
    (
      (port > 0)
      and (port <= 65535)
    )
  )
) TABLESPACE pg_default;

create trigger update_smtp_settings_updated_at BEFORE
update on smtp_settings for EACH row
execute FUNCTION update_updated_at_column ();



create table public.system_settings (
  id uuid not null default gen_random_uuid (),
  key character varying(100) not null,
  value jsonb not null,
  data_type public.data_type not null,
  category character varying(100) null,
  description text null,
  is_encrypted boolean null default false,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint system_settings_pkey primary key (id),
  constraint system_settings_key_key unique (key),
  constraint system_settings_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint system_settings_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create index IF not exists idx_system_settings_key on public.system_settings using btree (key) TABLESPACE pg_default;

create index IF not exists idx_system_settings_category on public.system_settings using btree (category) TABLESPACE pg_default;

create trigger update_system_settings_updated_at BEFORE
update on system_settings for EACH row
execute FUNCTION update_updated_at_column ();


create table public.user_contract_acceptance (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  contract_id uuid null,
  accepted_at timestamp with time zone null,
  ip_address inet null,
  user_agent text null,
  is_accepted boolean null default false,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  employee_id uuid null,
  contract_template_content text null,
  rejection_reason text null,
  hr_approved_by uuid null,
  hr_approved_at timestamp with time zone null,
  hr_rejection_reason text null,
  constraint user_contract_acceptance_pkey primary key (id),
  constraint user_contract_acceptance_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint user_contract_acceptance_employee_id_fkey foreign KEY (employee_id) references employees (id) on delete CASCADE,
  constraint fk_user_contract_acceptance_contract foreign KEY (contract_id) references contracts (id) on delete set null,
  constraint user_contract_acceptance_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint user_contract_acceptance_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE,
  constraint user_contract_acceptance_hr_approved_by_fkey foreign KEY (hr_approved_by) references auth.users (id)
) TABLESPACE pg_default;

create trigger update_user_contract_acceptance_updated_at BEFORE
update on user_contract_acceptance for EACH row
execute FUNCTION update_updated_at_column ();


create table public.user_permissions (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  permission_id uuid not null,
  can_view boolean null default false,
  can_add boolean null default false,
  can_edit boolean null default false,
  can_delete boolean null default false,
  assigned_at timestamp with time zone null default CURRENT_TIMESTAMP,
  assigned_by uuid null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint user_permissions_pkey primary key (id),
  constraint unique_user_permission unique (user_id, permission_id),
  constraint user_permissions_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint user_permissions_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint user_permissions_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE,
  constraint user_permissions_permission_id_fkey foreign KEY (permission_id) references permissions (id) on delete CASCADE,
  constraint user_permissions_assigned_by_fkey foreign KEY (assigned_by) references auth.users (id)
) TABLESPACE pg_default;

create unique INDEX IF not exists idx_user_permissions_active on public.user_permissions using btree (user_id, permission_id) TABLESPACE pg_default
where
  (is_deleted = false);

create index IF not exists idx_user_permissions_user on public.user_permissions using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_user_permissions_permission on public.user_permissions using btree (permission_id) TABLESPACE pg_default;

create trigger update_user_permissions_updated_at BEFORE
update on user_permissions for EACH row
execute FUNCTION update_updated_at_column ();



create table public.user_profiles (
  id uuid not null,
  first_name character varying(100) not null,
  middle_name character varying(100) null,
  last_name character varying(100) not null,
  personal_email character varying(255) null,
  phone character varying(20) null,
  date_of_birth date null,
  gender public.gender_type null,
  city character varying(100) null,
  state character varying(100) null,
  address text null,
  pincode character varying(20) null,
  biometric_code character varying(255) null,
  last_login_ip inet null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint user_profiles_pkey primary key (id),
  constraint user_profiles_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint user_profiles_id_fkey foreign KEY (id) references auth.users (id) on delete CASCADE,
  constraint user_profiles_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint valid_personal_email check (
    (
      (personal_email is null)
      or (
        (personal_email)::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_user_profiles_user_id on public.user_profiles using btree (id) TABLESPACE pg_default;

create trigger update_user_profiles_updated_at BEFORE
update on user_profiles for EACH row
execute FUNCTION update_updated_at_column ();



    create table public.user_roles (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  role_id uuid not null,
  assigned_at timestamp with time zone null default CURRENT_TIMESTAMP,
  assigned_by uuid null,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint user_roles_pkey primary key (id),
  constraint unique_user_role unique (user_id, role_id),
  constraint user_roles_assigned_by_fkey foreign KEY (assigned_by) references auth.users (id),
  constraint user_roles_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint user_roles_role_id_fkey foreign KEY (role_id) references roles (id) on delete CASCADE,
  constraint user_roles_updated_by_fkey foreign KEY (updated_by) references auth.users (id),
  constraint user_roles_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE
) TABLESPACE pg_default;

create unique INDEX IF not exists idx_user_roles_active on public.user_roles using btree (user_id, role_id) TABLESPACE pg_default
where
  (is_deleted = false);

create index IF not exists idx_user_roles_user on public.user_roles using btree (user_id) TABLESPACE pg_default;

create index IF not exists idx_user_roles_role on public.user_roles using btree (role_id) TABLESPACE pg_default;

create index IF not exists idx_user_roles_security on public.user_roles using btree (user_id, role_id, is_active) TABLESPACE pg_default;

create trigger audit_user_roles
after INSERT
or DELETE
or
update on user_roles for EACH row
execute FUNCTION create_audit_log ();

create trigger update_user_roles_updated_at BEFORE
update on user_roles for EACH row
execute FUNCTION update_updated_at_column ();



create table public.work_weeks (
  id uuid not null default gen_random_uuid (),
  name character varying(255) not null,
  monday boolean null default true,
  tuesday boolean null default true,
  wednesday boolean null default true,
  thursday boolean null default true,
  friday boolean null default true,
  saturday boolean null default false,
  sunday boolean null default false,
  is_active boolean null default true,
  is_deleted boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  created_by uuid null,
  updated_by uuid null,
  constraint work_weeks_pkey primary key (id),
  constraint work_weeks_created_by_fkey foreign KEY (created_by) references auth.users (id),
  constraint work_weeks_updated_by_fkey foreign KEY (updated_by) references auth.users (id)
) TABLESPACE pg_default;

create trigger update_work_weeks_updated_at BEFORE
update on work_weeks for EACH row
execute FUNCTION update_updated_at_column ();

