import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { Plus, Search, Eye, Download, Calculator, DollarSign, Users, Calendar, FileText } from "lucide-react"
import { Layout } from "@/components/Layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"

interface PayrollPeriod {
  id: string
  name: string
  start_date: string
  end_date: string
  year: number
  month: number
  status: 'draft' | 'processed' | 'confirmed'
}

interface PayrollRecord {
  id: string
  employee_id: string
  basic_salary: number
  gross_salary: number
  total_deductions: number
  net_salary: number
  working_days: number
  present_days: number
  status: 'draft' | 'processed' | 'confirmed'
  employees: {
    employee_code: string
    user_profiles: {
      first_name: string
      last_name: string
    }
  }
}

export default function Payroll() {
  const { toast } = useToast()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [payrollPeriods, setPayrollPeriods] = useState<PayrollPeriod[]>([])
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState<string>("")
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    fetchPayrollPeriods()
  }, [])

  useEffect(() => {
    if (selectedPeriod) {
      fetchPayrollRecords(selectedPeriod)
    }
  }, [selectedPeriod])

  const fetchPayrollPeriods = async () => {
    try {
      const { data, error } = await supabase
        .from('payroll_periods')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('year', { ascending: false })
        .order('month', { ascending: false })

      if (error) throw error
      setPayrollPeriods(data || [])
      if (data && data.length > 0) {
        setSelectedPeriod(data[0].id)
      }
    } catch (error) {
      console.error('Error fetching payroll periods:', error)
      toast({ title: "Error", description: "Failed to fetch payroll periods", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  const fetchPayrollRecords = async (periodId: string) => {
    try {
      // First check if payroll is calculated for this period
      const { data: existingPayroll } = await supabase
        .from('payroll')
        .select('id')
        .eq('payroll_period_id', periodId)
        .limit(1)

      if (existingPayroll && existingPayroll.length > 0) {
        // Show calculated payroll
        const { data, error } = await supabase
          .from('payroll')
          .select(`
            *,
            employees!inner(
              employee_code,
              user_id
            )
          `)
          .eq('payroll_period_id', periodId)
          .eq('is_active', true)
          .eq('is_deleted', false)

        if (error) throw error

        // Fetch user profiles
        const employeeUserIds = data?.map(p => p.employees.user_id) || []
        const { data: profiles } = await supabase
          .from('user_profiles')
          .select('id, first_name, last_name')
          .in('id', employeeUserIds)

        const recordsWithProfiles = data?.map(record => ({
          ...record,
          employees: {
            ...record.employees,
            user_profiles: profiles?.find(p => p.id === record.employees.user_id) || {
              first_name: '',
              last_name: ''
            }
          }
        })) || []

        setPayrollRecords(recordsWithProfiles)
      } else {
        // Show current salary structure from active contracts
        const { data: employees, error } = await supabase
          .from('employees')
          .select(`
            id,
            employee_code,
            user_id,
            contracts!inner(
              id,
              basic_salary,
              status
            )
          `)
          .eq('is_active', true)
          .eq('is_deleted', false)
          .eq('contracts.status', 'active')
          .eq('contracts.is_active', true)
          .eq('contracts.is_deleted', false)

        if (error) throw error

        // Fetch user profiles
        const employeeUserIds = employees?.map(emp => emp.user_id) || []
        const { data: profiles } = await supabase
          .from('user_profiles')
          .select('id, first_name, last_name')
          .in('id', employeeUserIds)

        // Get salary components for each employee
        const recordsWithSalary = await Promise.all(
          employees?.map(async (employee) => {
            const { data: salaryComponents } = await supabase
              .from('employee_salary_components')
              .select(`
                value,
                salary_components(
                  component_type
                )
              `)
              .eq('employee_id', employee.id)
              .eq('is_active', true)
              .eq('is_deleted', false)

            const earnings = salaryComponents?.filter(sc => sc.salary_components.component_type === 'earning').reduce((sum, sc) => sum + sc.value, 0) || 0
            const deductions = salaryComponents?.filter(sc => sc.salary_components.component_type === 'deduction').reduce((sum, sc) => sum + sc.value, 0) || 0

            return {
              id: `temp-${employee.id}`,
              employee_id: employee.id,
              basic_salary: earnings,
              gross_salary: earnings,
              total_deductions: deductions,
              net_salary: earnings - deductions,
              working_days: 0,
              present_days: 0,
              status: 'not_calculated',
              employees: {
                employee_code: employee.employee_code,
                user_id: employee.user_id,
                user_profiles: profiles?.find(p => p.id === employee.user_id) || {
                  first_name: '',
                  last_name: ''
                }
              }
            }
          }) || []
        )

        setPayrollRecords(recordsWithSalary)
      }
    } catch (error) {
      console.error('Error fetching payroll records:', error)
      toast({ title: "Error", description: "Failed to fetch payroll records", variant: "destructive" })
    }
  }

  const calculatePayroll = async () => {
    if (!selectedPeriod) return

    try {
      setLoading(true)
      const { error } = await supabase.rpc('calculate_payroll_to_date' as any, {
        p_payroll_period_id: selectedPeriod
      })

      if (error) throw error

      toast({ title: "Success", description: "Payroll calculated successfully for current date" })
      fetchPayrollPeriods()
      fetchPayrollRecords(selectedPeriod)
    } catch (error) {
      console.error('Error calculating payroll:', error)
      toast({ title: "Error", description: "Failed to calculate payroll", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  const processPayroll = async () => {
    if (!selectedPeriod) return

    try {
      const { error } = await supabase
        .from('payroll_periods')
        .update({ status: 'processed' })
        .eq('id', selectedPeriod)

      if (error) throw error

      const { error: payrollError } = await supabase
        .from('payroll')
        .update({ status: 'processed' })
        .eq('payroll_period_id', selectedPeriod)

      if (payrollError) throw payrollError

      toast({ title: "Success", description: "Payroll processed successfully" })
      fetchPayrollRecords(selectedPeriod)
      fetchPayrollPeriods()
    } catch (error) {
      console.error('Error processing payroll:', error)
      toast({ title: "Error", description: "Failed to process payroll", variant: "destructive" })
    }
  }

  const autoCreatePeriod = async () => {
    try {
      const { error } = await supabase.rpc('auto_create_current_month_period' as any)

      if (error) throw error
      fetchPayrollPeriods()
    } catch (error) {
      console.error('Error auto-creating period:', error)
    }
  }

  useEffect(() => {
    autoCreatePeriod()
  }, [])

  const getStatusBadge = (status: string) => {
    const styles = {
      not_calculated: "bg-gray-100 text-gray-800",
      draft: "bg-yellow-100 text-yellow-800",
      processed: "bg-blue-100 text-blue-800",
      confirmed: "bg-green-100 text-green-800"
    }
    const labels = {
      not_calculated: "Not Calculated",
      draft: "Calculated",
      processed: "Processed",
      confirmed: "Confirmed"
    }
    return <Badge className={styles[status as keyof typeof styles]}>{labels[status as keyof typeof labels] || status}</Badge>
  }

  const filteredRecords = payrollRecords.filter(record =>
    record.employees.user_profiles.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.employees.user_profiles.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.employees.employee_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalGross = payrollRecords.reduce((sum, record) => sum + record.gross_salary, 0)
  const totalNet = payrollRecords.reduce((sum, record) => sum + record.net_salary, 0)
  const totalDeductions = payrollRecords.reduce((sum, record) => sum + record.total_deductions, 0)

  return (
    <Layout>
      <div className="space-y-6">
        <Card>
          <CardHeader className="bg-gray-50 border-b">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div>
                <CardTitle>Payroll Management</CardTitle>
                <p className="text-muted-foreground mt-1 text-sm">Manage employee payroll and salary processing</p>
              </div>
              <div className="flex items-center gap-2">
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    {payrollPeriods.map(period => (
                      <SelectItem key={period.id} value={period.id}>
                        {period.name} ({period.year})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="outline" onClick={() => navigate('/payroll/periods')}>
                  <Plus className="mr-2 h-4 w-4" />
                  Manage Periods
                </Button>
                <Button onClick={calculatePayroll} disabled={!selectedPeriod}>
                  <Calculator className="mr-2 h-4 w-4" />
                  Calculate Payroll
                </Button>
                <Button onClick={processPayroll} disabled={!selectedPeriod} variant="default">
                  <FileText className="mr-2 h-4 w-4" />
                  Process Payroll
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs defaultValue="payroll" className="w-full">
              <TabsList className="border-b w-full justify-start rounded-none px-6">
                <TabsTrigger value="payroll">Payroll Records</TabsTrigger>
                <TabsTrigger value="summary">Summary</TabsTrigger>
              </TabsList>

              <TabsContent value="payroll" className="p-6">
                <div className="flex items-center gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search employees..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </Button>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Employee</TableHead>
                        <TableHead>Basic Salary</TableHead>
                        <TableHead>Gross Salary</TableHead>
                        <TableHead>Deductions</TableHead>
                        <TableHead>Net Salary</TableHead>
                        <TableHead>Days</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRecords.map(record => (
                        <TableRow key={record.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {record.employees.user_profiles.first_name} {record.employees.user_profiles.last_name}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {record.employees.employee_code}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>₹{record.basic_salary.toLocaleString()}</TableCell>
                          <TableCell>₹{record.gross_salary.toLocaleString()}</TableCell>
                          <TableCell>₹{record.total_deductions.toLocaleString()}</TableCell>
                          <TableCell className="font-medium">₹{record.net_salary.toLocaleString()}</TableCell>
                          <TableCell>
                          {record.status === 'not_calculated' ? '-/-' : `${record.present_days}/${record.working_days}`}
                        </TableCell>
                          <TableCell>{getStatusBadge(record.status)}</TableCell>
                          <TableCell>
                            <Button variant="ghost" size="icon">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="summary" className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Total Gross</p>
                          <p className="text-2xl font-bold text-green-600">₹{totalGross.toLocaleString()}</p>
                        </div>
                        <DollarSign className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Total Deductions</p>
                          <p className="text-2xl font-bold text-red-600">₹{totalDeductions.toLocaleString()}</p>
                        </div>
                        <FileText className="h-8 w-8 text-red-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Total Net</p>
                          <p className="text-2xl font-bold text-blue-600">₹{totalNet.toLocaleString()}</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </Layout>
  )
}