import { useState, useEffect } from "react"
import { useNavigate, useSearchParams } from "react-router-dom"
import { ArrowLeft, Calendar, User, FileText, DollarSign, Building, Clock, CheckSquare, AlertCircle, Save, X, Settings, Users, Briefcase, CreditCard, Plus, Minus } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { usePermissions } from "@/hooks/usePermissions";
// Interface definitions (unchanged)
interface Employee {
  id: string
  employee_code: string
  user_id: string
  user_profiles: {
    first_name: string
    last_name: string
  }
}

interface ContractType {
  id: string
  name: string
  code: string
  description: string
}

interface ContractGroup {
  id: string
  name: string
  employee_id: string
  start_date: string
  end_date: string
  employee?: {
    user_profiles?: {
      first_name: string
      last_name: string
    }
  }
}

interface ContractTemplate {
  id: string
  name: string
  content: string
}

interface Holiday {
  id: string
  name: string
  start_date: string
  end_date: string
  type: string
}

interface LeaveType {
  id: string
  name: string
  code: string
  description: string
}

interface RequiredDocument {
  id: string
  contract_type_id: string
  document_type: string
  is_mandatory: boolean
  remarks: string
}

interface SalaryComponent {
  id: string
  name: string
  code: string
  component_type: 'earning' | 'deduction'
  calculation_type: 'fixed' | 'percentage' | 'formula'
  default_value?: number
  percentage_of?: string
  formula?: string
  description?: string
  is_taxable: boolean
  is_active: boolean
}

interface EmployeeSalaryComponent {
  salary_component_id: string
  value: number
  component: SalaryComponent
}

export default function AddContract() {

   const { hasPermission, loading: permissionsLoading } = usePermissions();
  const navigate = useNavigate()
  const { toast } = useToast()
  const [searchParams] = useSearchParams()
  const groupId = searchParams.get('groupId')

  // All state variables remain the same
  const [loading, setLoading] = useState(false)
  const [employees, setEmployees] = useState<Employee[]>([])
  const [contractTypes, setContractTypes] = useState<ContractType[]>([])
  const [contractGroups, setContractGroups] = useState<ContractGroup[]>([])
  const [contractTemplates, setContractTemplates] = useState<ContractTemplate[]>([])
  const [holidays, setHolidays] = useState<Holiday[]>([])
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([])
  const [requiredDocuments, setRequiredDocuments] = useState<RequiredDocument[]>([])
  const [salaryComponents, setSalaryComponents] = useState<SalaryComponent[]>([])
  const [selectedSalaryComponents, setSelectedSalaryComponents] = useState<EmployeeSalaryComponent[]>([])
  const [selectedHolidays, setSelectedHolidays] = useState<string[]>([])
  const [selectedLeaves, setSelectedLeaves] = useState<{ leave_type_id: string; days_allowed: number; carry_forward: boolean; encashable: boolean; salary_payable: boolean }[]>([])
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [selectedGroup, setSelectedGroup] = useState<ContractGroup | null>(null)
  const [existingContracts, setExistingContracts] = useState<any[]>([])
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  const [formData, setFormData] = useState({
    employee_id: "",
    contract_group_id: groupId || "",
    contract_type_id: "",
    contract_template_id: "",
    overtime_allowed: false,
    overtime_rate: "",
    probation_period: "",
    notice_period: "",
    status: "draft"
  })

  // All useEffect hooks and logic functions remain unchanged
  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    if (formData.contract_type_id) {
      fetchContractTemplates(formData.contract_type_id)
      fetchRequiredDocuments(formData.contract_type_id)
    }
  }, [formData.contract_type_id])

  useEffect(() => {
    if (existingContracts.length > 0) {
      const hasActiveContract = existingContracts.some(contract => contract.status === 'active')
      if (hasActiveContract) {
        setFormData(prev => ({ ...prev, status: 'draft' }))
      }
    }
  }, [existingContracts])

  useEffect(() => {
    if (startDate || endDate) {
      validateContractDates()
    }
  }, [startDate, endDate, selectedGroup, existingContracts])

  const fetchData = async () => {
    try {
      // Fetch employees
      const { data: employeesData, error: employeesError } = await supabase
        .from('employees')
        .select(`id, employee_code, user_id`)
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('employee_code')
      if (employeesError) throw employeesError

      // Fetch user profiles for employees
      const employeeUserIds = employeesData?.map(emp => emp.user_id) || []
      const { data: profilesData } = await supabase
        .from('user_profiles')
        .select('id, first_name, last_name')
        .in('id', employeeUserIds)

      const employeesWithProfiles = employeesData?.map(emp => ({
        ...emp,
        user_profiles: profilesData?.find(p => p.id === emp.user_id) || { first_name: '', last_name: '' }
      })) || []
      setEmployees(employeesWithProfiles)

      // Fetch contract types
      const { data: typesData, error: typesError } = await supabase.from('contract_types').select('*').eq('is_active', true).eq('is_deleted', false).order('name')
      if (typesError) throw typesError
      setContractTypes(typesData || [])

      // Fetch contract groups
      const { data: groupsData, error: groupsError } = await supabase.from('contract_groups').select(`id, name, employee_id, start_date, end_date`).eq('is_active', true).eq('is_deleted', false).order('name')
      if (groupsError) throw groupsError
      setContractGroups(groupsData || [])

      // Fetch holidays
      const { data: holidaysData, error: holidaysError } = await supabase.from('holidays').select('id, name, start_date, end_date, type').eq('is_active', true).eq('is_deleted', false).order('name')
      if (holidaysError) throw holidaysError
      setHolidays(holidaysData || [])

      // Fetch leave types
      const { data: leaveTypesData, error: leaveTypesError } = await supabase.from('leave_types').select('id, name, code, description').eq('is_active', true).eq('is_deleted', false).order('name')
      if (leaveTypesError) throw leaveTypesError
      setLeaveTypes(leaveTypesData || [])

      // Fetch salary components
      const { data: salaryComponentsData, error: salaryComponentsError } = await supabase.from('salary_components').select('*').eq('is_active', true).eq('is_deleted', false).order('component_type', { ascending: false }).order('name')
      if (salaryComponentsError) throw salaryComponentsError
      setSalaryComponents(salaryComponentsData || [])

      if (groupId && groupsData) {
        const currentGroup = groupsData.find(g => g.id === groupId)
        if (currentGroup) {
          setFormData(prev => ({ ...prev, employee_id: currentGroup.employee_id }))
          setSelectedGroup(currentGroup)
          const { data: contractsData } = await supabase.from('contracts').select('*').eq('contract_group_id', groupId).eq('is_active', true).eq('is_deleted', false).order('end_date', { ascending: false })
          setExistingContracts(contractsData || [])
          if (contractsData && contractsData.length > 0) {
            const lastContract = contractsData[0]
            if (lastContract.end_date) {
              const nextStartDate = new Date(lastContract.end_date)
              nextStartDate.setDate(nextStartDate.getDate() + 1)
              setStartDate(nextStartDate)
            }
          } else {
            setStartDate(new Date(currentGroup.start_date))
          }
        }
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({ title: "Error", description: "Failed to fetch required data", variant: "destructive" })
    }
  }

  const fetchContractTemplates = async (contractTypeId: string) => {
    try {
      const { data, error } = await supabase.from('contract_templates').select('*').eq('contract_type_id', contractTypeId).eq('is_active', true).eq('is_deleted', false).order('name')
      if (error) throw error
      setContractTemplates(data || [])
      if (data && data.length > 0) {
        setFormData(prev => ({ ...prev, contract_template_id: data[0].id }))
      }
    } catch (error) {
      console.error('Error fetching contract templates:', error)
    }
  }

  const handleContractTypeChange = (contractTypeId: string) => {
    setFormData(prev => ({ ...prev, contract_type_id: contractTypeId, contract_template_id: '' }))
  }

  const fetchRequiredDocuments = async (contractTypeId: string) => {
    try {
      const { data, error } = await supabase.from('contract_type_required_documents').select('*').eq('contract_type_id', contractTypeId).eq('is_active', true).eq('is_deleted', false).order('document_type')
      if (error) throw error
      setRequiredDocuments(data || [])
    } catch (error) {
      console.error('Error fetching required documents:', error)
      setRequiredDocuments([])
    }
  }

  const validateContractDates = () => {
    const errors: string[] = []
    if (!startDate) {
      errors.push("Start date is required")
      setValidationErrors(errors)
      return false
    }
    if (!selectedGroup) {
      setValidationErrors([])
      return true
    }
    const groupStart = new Date(selectedGroup.start_date)
    const groupEnd = new Date(selectedGroup.end_date)
    if (startDate < groupStart) {
      errors.push(`Contract start date cannot be before group start date (${format(groupStart, "PPP")})`)
    }
    if (endDate && endDate > groupEnd) {
      errors.push(`Contract end date cannot be after group end date (${format(groupEnd, "PPP")})`)
    }
    if (existingContracts.length > 0) {
      const lastContract = existingContracts[0]
      const lastEndDate = new Date(lastContract.end_date)
      if (startDate <= lastEndDate) {
        errors.push(`New contract start date must be after previous contract end date (${format(lastEndDate, "PPP")})`)
      }
    }
    setValidationErrors(errors)
    return errors.length === 0
  }

  const calculateTotalSalary = () => {
    const earnings = selectedSalaryComponents.filter(sc => sc.component.component_type === 'earning').reduce((sum, sc) => sum + sc.value, 0)
    const deductions = selectedSalaryComponents.filter(sc => sc.component.component_type === 'deduction').reduce((sum, sc) => sum + sc.value, 0)
    return earnings - deductions
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.employee_id || !formData.contract_type_id || !startDate || selectedSalaryComponents.length === 0 || !validateContractDates()) {
      toast({ title: "Validation Error", description: "Please fill all required fields and fix validation errors.", variant: "destructive" })
      return
    }
    setLoading(true)
    try {
      const totalSalary = calculateTotalSalary()
      const contractData = {
        employee_id: formData.employee_id,
        contract_group_id: formData.contract_group_id || null,
        contract_type_id: formData.contract_type_id,
        contract_template_id: formData.contract_template_id || null,
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate ? endDate.toISOString().split('T')[0] : null,
        basic_salary: totalSalary,
        overtime_allowed: formData.overtime_allowed,
        overtime_rate: formData.overtime_rate ? parseFloat(formData.overtime_rate) : null,
        probation_period: formData.probation_period ? parseInt(formData.probation_period) : null,
        notice_period: formData.notice_period ? parseInt(formData.notice_period) : null,
        status: formData.status as "active" | "expired" | "terminated" | "draft"
      }
      const { data: contractResult, error } = await supabase.from('contracts').insert(contractData).select().single()
      if (error) throw error
      const contractId = contractResult.id

      if (selectedSalaryComponents.length > 0) {
        await supabase.from('employee_salary_components').update({ is_active: false }).eq('employee_id', formData.employee_id).eq('is_active', true)
        const salaryComponentInserts = selectedSalaryComponents.map(sc => ({
          employee_id: formData.employee_id,
          salary_component_id: sc.salary_component_id,
          value: sc.value,
          effective_from: startDate.toISOString().split('T')[0],
          effective_to: endDate ? endDate.toISOString().split('T')[0] : null,
          contract_id: contractId,
          is_active: true
        }))
        const { error: salaryComponentError } = await supabase.from('employee_salary_components').insert(salaryComponentInserts)
        if (salaryComponentError) throw salaryComponentError
      }

      if (selectedHolidays.length > 0) {
        const holidayInserts = selectedHolidays.map(holidayId => ({ contract_id: contractId, holiday_id: holidayId, is_applicable: true }))
        const { error: holidayError } = await supabase.from('contract_holidays').insert(holidayInserts)
        if (holidayError) throw holidayError
      }

      if (selectedLeaves.length > 0) {
        const leaveInserts = selectedLeaves.map(leave => ({ ...leave, contract_id: contractId }))
        const { error: leaveError } = await supabase.from('contract_leaves').insert(leaveInserts)
        if (leaveError) throw leaveError
        
        // Sync leave balances after creating contract leaves
        const { error: syncError } = await supabase.rpc('sync_leave_balances_from_contract' as any, {
          p_employee_id: formData.employee_id,
          p_contract_id: contractId,
          p_year: new Date().getFullYear()
        })
        
        if (syncError) {
          console.warn('Warning: Failed to sync leave balances:', syncError)
          // Don't throw error as contract creation was successful
        }
      }

      toast({ title: "Success", description: "Contract created successfully" })
      navigate('/contracts')
    } catch (error) {
      console.error('Error creating contract:', error)
      toast({ title: "Error", description: "Failed to create contract", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }
  if (permissionsLoading) { return <Layout><div>Loading...</div></Layout>; }

  // 👇 ADD THIS PAGE GUARD 👇
  if (!hasPermission('contracts', 'add')) {
    return (
      <Layout>
        <div className="p-8 text-center text-red-600">
          <h2>Access Denied</h2>
          <p>You do not have permission to add new contracts.</p>
        </div>
      </Layout>
    );
  }
  return (
    <Layout>
      <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 min-h-screen">
        <div className=" mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button variant="ghost" size="icon" onClick={() => navigate('/contracts')}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Create New Contract</h1>
              <p className="text-sm text-gray-600">Fill in the details to create a new employment contract.</p>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column */}
              <div className="lg:col-span-2 space-y-8">
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                    <CardDescription>Select the employee and contract type.</CardDescription>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="employee">Employee *</Label>
                      {groupId && formData.employee_id ? (
                        <div className="h-10 border rounded-md px-3 bg-gray-100 flex items-center">
                          <span className="font-medium text-sm text-gray-700">
                            {employees.find(emp => emp.id === formData.employee_id)?.user_profiles.first_name} {employees.find(emp => emp.id === formData.employee_id)?.user_profiles.last_name} (from group)
                          </span>
                        </div>
                      ) : (
                        <Select value={formData.employee_id} onValueChange={(value) => setFormData({ ...formData, employee_id: value })}>
                          <SelectTrigger><SelectValue placeholder="Select employee" /></SelectTrigger>
                          <SelectContent>
                            {employees.map((employee) => (
                              <SelectItem key={employee.id} value={employee.id}>
                                {employee.user_profiles.first_name} {employee.user_profiles.last_name} ({employee.employee_code})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contract_group">Contract Group</Label>
                      <Select value={formData.contract_group_id} disabled={!!groupId}>
                        <SelectTrigger>
                          <SelectValue placeholder={groupId ? selectedGroup?.name || "Selected group" : "Select group (optional)"} />
                        </SelectTrigger>
                        <SelectContent>
                          {contractGroups.map((group) => (
                            <SelectItem key={group.id} value={group.id}>{group.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contract_type">Contract Type *</Label>
                      <Select value={formData.contract_type_id} onValueChange={handleContractTypeChange}>
                        <SelectTrigger><SelectValue placeholder="Select contract type" /></SelectTrigger>
                        <SelectContent>
                          {contractTypes.map((type) => (
                            <SelectItem key={type.id} value={type.id}>{type.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contract_template">Contract Template</Label>
                      <Select value={formData.contract_template_id} onValueChange={(value) => setFormData({ ...formData, contract_template_id: value })}>
                        <SelectTrigger><SelectValue placeholder="Select template" /></SelectTrigger>
                        <SelectContent>
                          {contractTemplates.map((template) => (
                            <SelectItem key={template.id} value={template.id}>{template.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Timeline & Policy */}
                <Card>
                  <CardHeader>
                    <CardTitle>Timeline & Policies</CardTitle>
                    <CardDescription>Define contract dates and company policies.</CardDescription>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label>Start Date *</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className={cn("w-full justify-start text-left font-normal", !startDate && "text-muted-foreground")}>
                            <Calendar className="mr-2 h-4 w-4" />
                            {startDate ? format(startDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0"><CalendarComponent mode="single" selected={startDate} onSelect={setStartDate} initialFocus /></PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label>End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className={cn("w-full justify-start text-left font-normal", !endDate && "text-muted-foreground")}>
                            <Calendar className="mr-2 h-4 w-4" />
                            {endDate ? format(endDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0"><CalendarComponent mode="single" selected={endDate} onSelect={setEndDate} initialFocus /></PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="probation_period">Probation Period (months)</Label>
                      <Input id="probation_period" type="number" value={formData.probation_period} onChange={(e) => setFormData({ ...formData, probation_period: e.target.value })} placeholder="e.g., 3" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="notice_period">Notice Period (days)</Label>
                      <Input id="notice_period" type="number" value={formData.notice_period} onChange={(e) => setFormData({ ...formData, notice_period: e.target.value })} placeholder="e.g., 30" />
                    </div>
                  </CardContent>
                </Card>

                {/* Financial Details */}
                <Card>
                  <CardHeader>
                    <CardTitle>Financial Details</CardTitle>
                    <CardDescription>Configure salary and overtime settings.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <Label className="font-semibold">Salary Components *</Label>
                      <div className="max-h-80 overflow-y-auto space-y-3 border rounded-lg p-4 bg-gray-50">
                        {salaryComponents.length > 0 ? salaryComponents.map((component) => {
                          const selectedComponent = selectedSalaryComponents.find(sc => sc.salary_component_id === component.id);
                          return (
                            <div key={component.id} className="p-3 bg-white rounded-md border">
                              <div className="flex items-start justify-between">
                                <div className="flex items-center gap-3">
                                  <Checkbox
                                    id={`comp-${component.id}`}
                                    checked={!!selectedComponent}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setSelectedSalaryComponents([...selectedSalaryComponents, { salary_component_id: component.id, value: component.default_value || 0, component }]);
                                      } else {
                                        setSelectedSalaryComponents(selectedSalaryComponents.filter(sc => sc.salary_component_id !== component.id));
                                      }
                                    }}
                                  />
                                  <div>
                                    <Label htmlFor={`comp-${component.id}`} className="font-medium text-gray-800">{component.name}</Label>
                                    <p className="text-xs text-gray-500">{component.code} - {component.component_type}</p>
                                  </div>
                                </div>
                                {selectedComponent && (
                                  <div className="w-40">
                                    <Input
                                      type="number"
                                      step="0.01"
                                      min="0"
                                      value={selectedComponent.value}
                                      onChange={(e) => {
                                        const updated = selectedSalaryComponents.map(sc => sc.salary_component_id === component.id ? { ...sc, value: parseFloat(e.target.value) || 0 } : sc);
                                        setSelectedSalaryComponents(updated);
                                      }}
                                      className="h-8 text-right"
                                      placeholder="Amount"
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        }) : <p className="text-sm text-center text-gray-500 py-4">No salary components available.</p>}
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="overtime_allowed" checked={formData.overtime_allowed} onCheckedChange={(checked) => setFormData({ ...formData, overtime_allowed: checked as boolean })} />
                        <Label htmlFor="overtime_allowed">Allow Overtime Work</Label>
                      </div>
                      {formData.overtime_allowed && (
                        <div className="space-y-2 pl-6">
                          <Label htmlFor="overtime_rate">Overtime Rate (per hour)</Label>
                          <Input id="overtime_rate" type="number" value={formData.overtime_rate} onChange={(e) => setFormData({ ...formData, overtime_rate: e.target.value })} placeholder="Enter rate" />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Leave Configuration */}
                <Card>
                  <CardHeader>
                    <CardTitle>Leave Configuration</CardTitle>
                    <CardDescription>Set up applicable leave types and policies for this contract.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {leaveTypes.map((leaveType) => {
                      const selectedLeave = selectedLeaves.find(l => l.leave_type_id === leaveType.id);
                      return (
                        <div key={leaveType.id} className="p-4 border rounded-lg bg-gray-50">
                          <div className="flex items-center space-x-3">
                            <Checkbox
                              id={`leave-${leaveType.id}`}
                              checked={!!selectedLeave}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedLeaves([...selectedLeaves, { leave_type_id: leaveType.id, days_allowed: 0, carry_forward: false, encashable: false, salary_payable: true }]);
                                } else {
                                  setSelectedLeaves(selectedLeaves.filter(l => l.leave_type_id !== leaveType.id));
                                }
                              }}
                            />
                            <Label htmlFor={`leave-${leaveType.id}`} className="font-medium text-gray-800">{leaveType.name}</Label>
                          </div>
                          {selectedLeave && (
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pl-8">
                              <div className="space-y-1">
                                <Label htmlFor={`days-${leaveType.id}`} className="text-xs">Days</Label>
                                <Input id={`days-${leaveType.id}`} type="number" value={selectedLeave.days_allowed}
                                  onChange={(e) => setSelectedLeaves(selectedLeaves.map(l => l.leave_type_id === leaveType.id ? { ...l, days_allowed: parseInt(e.target.value) || 0 } : l))}
                                  className="h-9"
                                />
                              </div>
                              <div className="flex items-end pb-1">
                                <div className="flex items-center space-x-2">
                                  <Checkbox id={`carry-${leaveType.id}`} checked={selectedLeave.carry_forward} onCheckedChange={(c) => setSelectedLeaves(selectedLeaves.map(l => l.leave_type_id === leaveType.id ? { ...l, carry_forward: c as boolean } : l))} />
                                  <Label htmlFor={`carry-${leaveType.id}`} className="text-xs">Carry Fwd</Label>
                                </div>
                              </div>
                              <div className="flex items-end pb-1">
                                <div className="flex items-center space-x-2">
                                  <Checkbox id={`encash-${leaveType.id}`} checked={selectedLeave.encashable} onCheckedChange={(c) => setSelectedLeaves(selectedLeaves.map(l => l.leave_type_id === leaveType.id ? { ...l, encashable: c as boolean } : l))} />
                                  <Label htmlFor={`encash-${leaveType.id}`} className="text-xs">Encashable</Label>
                                </div>
                              </div>
                              <div className="flex items-end pb-1">
                                <div className="flex items-center space-x-2">
                                  <Checkbox id={`payable-${leaveType.id}`} checked={selectedLeave.salary_payable} onCheckedChange={(c) => setSelectedLeaves(selectedLeaves.map(l => l.leave_type_id === leaveType.id ? { ...l, salary_payable: c as boolean } : l))} />
                                  <Label htmlFor={`payable-${leaveType.id}`} className="text-xs">Payable</Label>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </CardContent>
                </Card>

              </div>

              {/* Right Column */}
              <div className="space-y-8">
                {/* Actions and Status */}
                <Card>
                  <CardHeader>
                    <CardTitle>Finalize Contract</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="status">Contract Status</Label>
                      <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                        <SelectTrigger><SelectValue /></SelectTrigger>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="terminated">Terminated</SelectItem>
                          <SelectItem value="expired">Expired</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="p-4 border rounded-lg bg-gray-50 text-center">
                      <p className="text-sm text-gray-600">Total Salary</p>
                      <p className="text-3xl font-bold text-gray-800">
                        ₹{calculateTotalSalary().toLocaleString()}
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex gap-2">
                    <Button type="button" variant="outline" onClick={() => navigate('/contracts')} className="w-full">
                      <X className="h-4 w-4 mr-2" /> Cancel
                    </Button>
                    <Button type="submit" disabled={loading || validationErrors.length > 0} className="w-full">
                      {loading ? 'Creating...' : <><Save className="h-4 w-4 mr-2" /> Create Contract</>}
                    </Button>
                  </CardFooter>
                </Card>

                {/* Validation Errors */}
                {validationErrors.length > 0 && (
                  <Card className="bg-red-50 border-red-200">
                    <CardHeader>
                      <CardTitle className="text-base text-red-800 flex items-center gap-2"><AlertCircle className="h-5 w-5" /> Validation Errors</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="text-sm text-red-700 space-y-1 list-disc pl-5">
                        {validationErrors.map((error, index) => <li key={index}>{error}</li>)}
                      </ul>
                    </CardContent>
                  </Card>
                )}

                {/* Required Documents */}
                {requiredDocuments.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Required Documents</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {requiredDocuments.map((doc) => (
                        <div key={doc.id} className="flex items-center justify-between text-sm p-2 rounded-md bg-gray-50">
                          <p className="text-gray-700">{doc.document_type}</p>
                          {doc.is_mandatory ? (
                            <span className="text-xs font-semibold text-red-600">Required</span>
                          ) : (
                            <span className="text-xs text-gray-500">Optional</span>
                          )}
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}

                {/* Holidays */}
                <Card>
                  <CardHeader>
                    <CardTitle>Contract Holidays</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2 max-h-60 overflow-y-auto">
                    {holidays.map((holiday) => (
                      <div key={holiday.id} className="flex items-center space-x-3 p-2 rounded-md hover:bg-gray-50">
                        <Checkbox
                          id={`holiday-${holiday.id}`}
                          checked={selectedHolidays.includes(holiday.id)}
                          onCheckedChange={(checked) => {
                            setSelectedHolidays(
                              checked ? [...selectedHolidays, holiday.id] : selectedHolidays.filter(id => id !== holiday.id)
                            );
                          }}
                        />
                        <Label htmlFor={`holiday-${holiday.id}`} className="text-sm font-medium cursor-pointer flex-1">
                          {holiday.name}
                        </Label>
                      </div>
                    ))}
                  </CardContent>
                </Card>

              </div>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  )
}
