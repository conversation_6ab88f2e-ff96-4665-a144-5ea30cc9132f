import { useState, useEffect } from "react"
import { User, Calendar, Briefcase, Phone, Mail, MapPin, Download, FileText, PiggyBank, LogOut, ArrowRight } from "lucide-react"
import { Layout } from "@/components/Layout"
import { useAuth } from "@/contexts/AuthContext"
import { supabase } from "@/integrations/supabase/client"
import { format } from "date-fns"
import { Badge } from "@/components/ui/badge"

// --- MOCK DATA (to demonstrate new components) ---
const leaveData = {
  annual: { total: 20, used: 8 },
  sick: { total: 10, used: 2 },
  other: { total: 5, used: 1 },
}

const payslipsData = [
  { id: 1, period: "July 2025", amount: "₹85,250", date: "2025-08-01", url: "#" },
  { id: 2, period: "June 2025", amount: "₹85,100", date: "2025-07-01", url: "#" },
  { id: 3, period: "May 2025", amount: "₹84,950", date: "2025-06-01", url: "#" },
]

const upcomingHolidays = [
  { name: "Independence Day", date: "Aug 15, 2025" },
  { name: "<PERSON><PERSON><PERSON>", date: "Sep 7, 2025" },
  { name: "Diwali", date: "Oct 21, 2025" },
]


export default function EmployeeDashboard() {
  const { user } = useAuth()
  const [employee, setEmployee] = useState<any>(null)
  const [userProfile, setUserProfile] = useState<any>(null)
  const [contracts, setContracts] = useState<any[]>([])
  const [documents, setDocuments] = useState<any[]>([])
  const [bankDetails, setBankDetails] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('contracts');

  useEffect(() => {
    if (user) {
      fetchData()
    }
  }, [user])

  // --- DATA FETCHING (Same as original, remains unchanged) ---
  const fetchData = async () => {
    try {
      const { data: empData, error: empError } = await supabase
        .from('employees')
        .select('*')
        .eq('user_id', user?.id)
        .single()
      if (empError && empError.code !== 'PGRST116') throw empError;

      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user?.id)
        .single()
      if (profileError && profileError.code !== 'PGRST116') throw profileError;

      if (empData) {
        const { data: contractData } = await supabase.from('contracts').select('*, contract_types(name, code)').eq('employee_id', empData.id)
        setContracts(contractData || [])

        const { data: docData } = await supabase.from('employee_documents').select('*').eq('employee_id', empData.id)
        setDocuments(docData || [])

        const { data: bankData } = await supabase.from('employee_bank_details').select('*').eq('employee_id', empData.id)
        setBankDetails(bankData || [])
      }

      setEmployee(empData)
      setUserProfile(profileData)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[calc(100vh-200px)]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

          {/* --- LEFT COLUMN --- */}
          <div className="lg:col-span-2 space-y-8">
            {/* Employee Profile Card */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="flex flex-col sm:flex-row items-center gap-6">
                <img
                  src={userProfile?.avatar_url || `https://api.dicebear.com/8.x/initials/svg?seed=${userProfile?.first_name || 'U'}`}
                  alt="Profile"
                  className="w-24 h-24 rounded-full border-4 border-indigo-200 dark:border-indigo-800 object-cover"
                />
                <div className="text-center sm:text-left">
                  <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
                    Welcome, {userProfile?.first_name || user?.email?.split('@')[0]}!
                  </h1>
                  <p className="text-indigo-600 dark:text-indigo-400 font-medium mt-1">{employee?.job_title || "Employee"}</p>
                  <Badge variant={employee?.employment_status === 'active' ? 'default' : 'secondary'} className="mt-2 capitalize">
                    {employee?.employment_status || 'N/A'}
                  </Badge>
                </div>
              </div>
              <div className="border-t dark:border-gray-700 my-6"></div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 text-sm">
                <InfoItem icon={Briefcase} label="Employee ID" value={employee?.employee_code || 'N/A'} />
                <InfoItem icon={Calendar} label="Hire Date" value={employee?.hire_date ? format(new Date(employee.hire_date), 'MMM dd, yyyy') : 'N/A'} />
                <InfoItem icon={Mail} label="Company Email" value={employee?.company_email || 'N/A'} />
                <InfoItem icon={Phone} label="Phone" value={userProfile?.phone || 'N/A'} />
                <InfoItem icon={MapPin} label="Address" value={userProfile?.address || 'N/A'} />
                <InfoItem icon={User} label="Manager" value={employee?.manager_name || 'N/A'} />
              </div>
            </div>

            {/* Official Information Tabs */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                <h2 className="text-xl font-bold text-gray-800 dark:text-white">Official Information</h2>
                <div className="border-b dark:border-gray-700 mt-4">
                  <nav className="flex space-x-6">
                    <TabButton id="contracts" label="Contracts" activeTab={activeTab} setActiveTab={setActiveTab} />
                    <TabButton id="documents" label="Documents" activeTab={activeTab} setActiveTab={setActiveTab} />
                    <TabButton id="banking" label="Bank Details" activeTab={activeTab} setActiveTab={setActiveTab} />
                  </nav>
                </div>
              </div>
              <div className="px-6 pb-6">
                {activeTab === 'contracts' && <ContractsTab data={contracts} />}
                {activeTab === 'documents' && <DocumentsTab data={documents} />}
                {activeTab === 'banking' && <BankingTab data={bankDetails} />}
              </div>
            </div>
          </div>

          {/* --- RIGHT COLUMN --- */}
          <div className="space-y-8">
            {/* Leave Balance */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4">Leave Balance</h3>
              <div className="space-y-4">
                <LeaveType type="Annual" remaining={leaveData.annual.total - leaveData.annual.used} total={leaveData.annual.total} color="indigo" />
                <LeaveType type="Sick" remaining={leaveData.sick.total - leaveData.sick.used} total={leaveData.sick.total} color="amber" />
                <LeaveType type="Other" remaining={leaveData.other.total - leaveData.other.used} total={leaveData.other.total} color="sky" />
              </div>
              <button className="w-full mt-6 flex items-center justify-center gap-2 text-sm font-semibold text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 transition-colors">
                Request Leave <ArrowRight className="w-4 h-4" />
              </button>
            </div>

            {/* Recent Payslips */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4">Recent Payslips</h3>
              <div className="space-y-3">
                {payslipsData.map(p => <PayslipRow key={p.id} payslip={p} />)}
              </div>
            </div>

            {/* Upcoming Holidays */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4">Upcoming Holidays</h3>
              <ul className="space-y-3">
                {upcomingHolidays.map(h => (
                  <li key={h.name} className="flex items-center justify-between text-sm">
                    <p className="text-gray-600 dark:text-gray-300">{h.name}</p>
                    <p className="font-medium text-gray-800 dark:text-gray-100">{h.date}</p>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

// --- SUB-COMPONENTS ---
const InfoItem = ({ icon: Icon, label, value }) => (
  <div className="flex items-start gap-3">
    <Icon className="w-5 h-5 mt-1 text-gray-400" />
    <div>
      <p className="text-xs text-gray-500 dark:text-gray-400">{label}</p>
      <p className="font-medium text-gray-700 dark:text-gray-200">{value}</p>
    </div>
  </div>
)

const TabButton = ({ id, label, activeTab, setActiveTab }) => (
  <button
    onClick={() => setActiveTab(id)}
    className={`px-1 pb-2 text-sm font-semibold transition-colors border-b-2
            ${activeTab === id
        ? 'border-indigo-600 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
        : 'border-transparent text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200'}`
    }
  >
    {label}
  </button>
)

const LeaveType = ({ type, remaining, total, color }) => {
  const percentage = (remaining / total) * 100;
  const colors = {
    indigo: 'bg-indigo-500',
    amber: 'bg-amber-500',
    sky: 'bg-sky-500'
  };
  return (
    <div>
      <div className="flex justify-between mb-1 text-sm">
        <span className="font-medium text-gray-700 dark:text-gray-300">{type}</span>
        <span className="text-gray-500 dark:text-gray-400">{remaining} / {total} days</span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div className={`${colors[color]} h-2 rounded-full`} style={{ width: `${percentage}%` }}></div>
      </div>
    </div>
  );
}

const PayslipRow = ({ payslip }) => (
  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
    <div className="flex items-center gap-3">
      <FileText className="w-5 h-5 text-indigo-500" />
      <div>
        <p className="font-semibold text-sm text-gray-800 dark:text-gray-200">{payslip.period}</p>
        <p className="text-xs text-gray-500 dark:text-gray-400">{payslip.amount}</p>
      </div>
    </div>
    <a href={payslip.url} target="_blank" rel="noopener noreferrer" className="p-2 text-gray-500 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600">
      <Download className="w-4 h-4" />
    </a>
  </div>
)

// --- TAB CONTENT COMPONENTS ---

const ContractsTab = ({ data }) => (
  data.length > 0 ? (
    <div className="space-y-4">
      {data.map((c: any) => (
        <div key={c.id} className="border dark:border-gray-700 rounded-lg p-4">
          <div className="flex justify-between items-start mb-3">
            <h3 className="font-bold text-gray-800 dark:text-white">{c.contract_types?.name || 'Contract'}</h3>
            <Badge variant={c.status === 'active' ? 'default' : 'secondary'} className="capitalize">{c.status}</Badge>
            {/* <Badge variant={c.status === 'active' ? 'success' : 'secondary'} className="capitalize">{c.status}</Badge> */}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <InfoItem icon={Calendar} label="Start Date" value={c.start_date ? format(new Date(c.start_date), 'MMM dd, yyyy') : 'N/A'} />
            <InfoItem icon={Calendar} label="End Date" value={c.end_date ? format(new Date(c.end_date), 'MMM dd, yyyy') : 'Ongoing'} />
            <InfoItem icon={PiggyBank} label="Basic Salary" value={`₹${c.basic_salary?.toLocaleString() || 'N/A'}`} />
          </div>
        </div>
      ))}
    </div>
  ) : <p className="text-center text-gray-500 dark:text-gray-400 py-8">No contracts found.</p>
);


const DocumentsTab = ({ data }) => (
  data.length > 0 ? (
    <div className="space-y-3">
      {data.map((doc: any) => (
        <div key={doc.id} className="flex items-center justify-between border dark:border-gray-700 rounded-lg p-3">
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6 text-indigo-500 flex-shrink-0" />
            <div>
              <p className="font-semibold text-gray-800 dark:text-white">{doc.document_type}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Uploaded: {doc.uploaded_at ? format(new Date(doc.uploaded_at), 'PP') : 'N/A'}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant={doc.is_verified ? 'default' : 'secondary'}>{doc.is_verified ? 'Verified' : 'Pending'}</Badge>
            {doc.file_url && <a href={doc.file_url} target="_blank" rel="noopener noreferrer" className="p-2 text-gray-500 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600"><Download className="w-4 h-4" /></a>}
          </div>
        </div>
      ))}
    </div>
  ) : <p className="text-center text-gray-500 dark:text-gray-400 py-8">No documents found.</p>
);

const BankingTab = ({ data }) => (
  data.length > 0 ? (
    <div className="space-y-4">
      {data.map((bank: any, index: number) => (
        <div key={index} className="border dark:border-gray-700 rounded-lg p-4">
          <h3 className="font-bold text-gray-800 dark:text-white mb-3">Bank Account {index + 1}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <InfoItem icon={Briefcase} label="Bank Name" value={bank.bank_name || 'N/A'} />
            <InfoItem icon={User} label="Account Holder" value={bank.account_holder_name || 'N/A'} />
            <InfoItem icon={FileText} label="Account Number" value={bank.account_number ? `**** ${bank.account_number.slice(-4)}` : 'N/A'} />
            <InfoItem icon={FileText} label="IFSC Code" value={bank.ifsc_code || 'N/A'} />
          </div>
        </div>
      ))}
    </div>
  ) : <p className="text-center text-gray-500 dark:text-gray-400 py-8">No banking details found.</p>
);