import { Layout } from "@/components/Layout";
import { usePermissions } from "@/hooks/usePermissions";
import { useAuth } from "@/contexts/AuthContext";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';
import { Users, Briefcase, UserPlus, FileText, Gift, Calendar, TrendingUp, CheckCircle, Clock, ArrowRight, Sun } from 'lucide-react';

// --- Mock Data and Reusable Components (can be moved to separate files later) ---

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="p-4 bg-gray-800 text-white rounded-lg shadow-lg border border-gray-700">
        <p className="label font-bold text-sm">{`${label}`}</p>
        {payload.map((p: any, index: number) => (
          <p key={index} style={{ color: p.color }}>{`${p.name}: ${p.value}`}</p>
        ))}
      </div>
    );
  }
  return null;
};

const StatCard = ({ icon: Icon, title, value, trend, color }: any) => {
  const colors: Record<string, { bg: string, text: string }> = {
    indigo: { bg: 'bg-indigo-100 dark:bg-indigo-900/50', text: 'text-indigo-600 dark:text-indigo-400' },
    green: { bg: 'bg-green-100 dark:bg-green-900/50', text: 'text-green-600 dark:text-green-400' },
    amber: { bg: 'bg-amber-100 dark:bg-amber-900/50', text: 'text-amber-600 dark:text-amber-400' },
    sky: { bg: 'bg-sky-100 dark:bg-sky-900/50', text: 'text-sky-600 dark:text-sky-400' },
  };
  return (
    <div className="bg-white dark:bg-gray-800 p-5 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 flex items-center space-x-4">
      <div className={`p-3 rounded-full ${colors[color].bg}`}>
        <Icon className={`w-6 h-6 ${colors[color].text}`} />
      </div>
      <div>
        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</p>
        <p className="text-2xl font-bold text-gray-800 dark:text-white">{value}</p>
        <p className="text-xs text-gray-400 dark:text-gray-500">{trend}</p>
      </div>
    </div>
  );
};

// --- Role-Specific Widget Components ---

const AdminDashboardWidgets = () => {
  const departmentData = [
    { name: 'Engineering', value: 75, color: '#8B5CF6' },
    { name: 'Marketing', value: 25, color: '#3B82F6' },
    { name: 'Sales', value: 42, color: '#10B981' },
    { name: 'HR', value: 10, color: '#F59E0B' },
    { name: 'Support', value: 33, color: '#EF4444' }
  ];
  const totalEmployees = departmentData.reduce((acc, curr) => acc + curr.value, 0);

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard icon={Users} title="Total Employees" value={totalEmployees} trend="+5 this month" color="indigo" />
        <StatCard icon={CheckCircle} title="Present Today" value="175" trend="97% attendance" color="green" />
        <StatCard icon={Clock} title="On Leave" value="5" trend="2 new requests" color="amber" />
        <StatCard icon={Briefcase} title="Active Projects" value="12" trend="+1 from last week" color="sky" />
      </div>
      <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Employees by Department</h3>
        <ResponsiveContainer width="100%" height={350}>
          <BarChart data={departmentData}>
            <XAxis dataKey="name" tick={{ fill: 'rgb(107 114 128)' }} />
            <YAxis tick={{ fill: 'rgb(107 114 128)' }} />
            <Tooltip content={<CustomTooltip />} cursor={{ fill: 'rgba(128, 128, 128, 0.1)' }} />
            <Bar dataKey="value" name="Employees" fill="#8B5CF6" radius={[8, 8, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </>
  );
};

const HRDashboardWidgets = () => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
    <StatCard icon={UserPlus} title="New Hires" value="4" trend="This month" color="indigo" />
    <StatCard icon={FileText} title="Pending Approvals" value="2" trend="Onboarding" color="amber" />
    <StatCard icon={Gift} title="Upcoming Birthdays" value="7" trend="Next 30 days" color="green" />
    <StatCard icon={Calendar} title="Leave Requests" value="3" trend="Pending" color="sky" />
  </div>
);

const EmployeeDashboardWidgets = () => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
    <StatCard icon={Calendar} title="My Leave Balance" value="12 Days" trend="Annual Leave" color="green" />
    <StatCard icon={TrendingUp} title="My Attendance" value="98%" trend="This month" color="indigo" />
    <StatCard icon={Clock} title="Pending Tasks" value="3" trend="Check your inbox" color="amber" />
  </div>
);


// --- Main Dashboard Component ---

const Dashboard = () => {
  const { user, userRole, loading: authLoading } = useAuth();
  const { canAccess, loading: permissionsLoading } = usePermissions();

  if (authLoading || permissionsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-indigo-600"></div>
      </div>
    );
  }

  const getDashboardTitle = () => {
    switch(userRole) {
      case 'Admin': return "Admin Dashboard";
      case 'HR Manager': return "HR Dashboard";
      case 'Employee': return "My Dashboard";
      default: return "Dashboard";
    }
  }

  return (
    <Layout>
      <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="space-y-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 dark:text-white">{getDashboardTitle()}</h1>
              <p className="mt-1 text-gray-500 dark:text-gray-400">Welcome back, {user?.user_metadata.first_name || 'User'}!</p>
            </div>
             <div className="mt-4 sm:mt-0 flex items-center space-x-2 text-sm font-medium bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-sm">
               <Sun className="h-5 w-5 text-yellow-500" />
               <span>{new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</span>
             </div>
          </div>

          {/* Conditionally Rendered Widgets */}
          {canAccess('dashboard_admin') && <AdminDashboardWidgets />}
          {canAccess('dashboard_hr') && <HRDashboardWidgets />}
          {canAccess('dashboard_employee') && <EmployeeDashboardWidgets />}

        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
