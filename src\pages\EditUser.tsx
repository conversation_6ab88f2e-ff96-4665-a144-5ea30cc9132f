import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom"
import { ArrowLeft, AlertCircle } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { usePermissions } from "@/hooks/usePermissions";
export default function EditUser() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { hasPermission, loading: permissionsLoading } = usePermissions();
  
  // Form validation states
  const [errors, setErrors] = useState({
    first_name: "",
    last_name: "",
    personal_email: "",
    phone: "",
    biometric_code: "",
    role: ""
  })

  // Checking states
  const [isCheckingEmail, setIsCheckingEmail] = useState(false)
  const [isCheckingBiometricCode, setIsCheckingBiometricCode] = useState(false)
  
  const [formData, setFormData] = useState({
    first_name: "",
    middle_name: "",
    last_name: "",
    personal_email: "",
    phone: "",
    date_of_birth: "",
    gender: "",
    city: "",
    state: "",
    address: "",
    pincode: "",
    biometric_code: "",
    role: "",
    company_email: "",
    is_active: true
  })

  useEffect(() => {
    if (id) {
      fetchUser()
    }
  }, [id])

  // Validate email format
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Check if email already exists (excluding current user)
  const checkEmailExists = async (email: string) => {
    if (!email || !validateEmail(email) || !id) return
    
    setIsCheckingEmail(true)
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('personal_email')
        .eq('personal_email', email)
        .neq('id', id) // Exclude current user
        .maybeSingle()

      if (error) throw error
      
      if (data) {
        setErrors(prev => ({ ...prev, personal_email: "This email is already in use by another user" }))
      } else {
        setErrors(prev => ({ ...prev, personal_email: "" }))
      }
    } catch (error) {
      console.error('Error checking email:', error)
    } finally {
      setIsCheckingEmail(false)
    }
  }

  // Check if biometric code already exists (excluding current user)
  const checkBiometricCodeExists = async (code: string) => {
    if (!code || !id) return
    
    setIsCheckingBiometricCode(true)
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('biometric_code')
        .eq('biometric_code', code)
        .neq('id', id) // Exclude current user
        .maybeSingle()

      if (error) throw error
      
      if (data) {
        setErrors(prev => ({ ...prev, biometric_code: "This biometric code is already in use" }))
      } else {
        setErrors(prev => ({ ...prev, biometric_code: "" }))
      }
    } catch (error) {
      console.error('Error checking biometric code:', error)
    } finally {
      setIsCheckingBiometricCode(false)
    }
  }

  // Validate phone number
  const validatePhone = (phone: string) => {
    if (!phone) return true
    const phoneRegex = /^\d{10}$/
    return phoneRegex.test(phone)
  }

  // Handle input changes with validation
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Only validate string fields
    if (typeof value !== 'string') return
    
    // Validate based on field
    switch (field) {
      case 'first_name':
        setErrors(prev => ({ ...prev, first_name: value ? "" : "First name is required" }))
        break
      case 'last_name':
        setErrors(prev => ({ ...prev, last_name: value ? "" : "Last name is required" }))
        break
      case 'personal_email':
        if (!value) {
          setErrors(prev => ({ ...prev, personal_email: "Email is required" }))
        } else if (!validateEmail(value)) {
          setErrors(prev => ({ ...prev, personal_email: "Please enter a valid email" }))
        } else {
          // Only check for duplicates if format is valid
          checkEmailExists(value)
        }
        break
      case 'phone':
        setErrors(prev => ({ 
          ...prev, 
          phone: value && !validatePhone(value) ? "Please enter a valid 10-digit phone number" : "" 
        }))
        break
      case 'biometric_code':
        if (value) {
          checkBiometricCodeExists(value)
        } else {
          setErrors(prev => ({ ...prev, biometric_code: "" }))
        }
        break
      case 'role':
        setErrors(prev => ({ ...prev, role: value ? "" : "Role is required" }))
        break
      default:
        break
    }
  }

  const fetchUser = async () => {
    try {
      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', id)
        .single()

      if (profileError) throw profileError

      // Get user role
      const { data: userRole } = await supabase
        .from('user_roles')
        .select('roles(name)')
        .eq('user_id', id)
        .single()

      // Get employee data if exists
      const { data: employee } = await supabase
        .from('employees')
        .select('company_email')
        .eq('user_id', id)
        .single()

      setFormData({
        first_name: profile.first_name || "",
        middle_name: profile.middle_name || "",
        last_name: profile.last_name || "",
        personal_email: profile.personal_email || "",
        phone: profile.phone || "",
        date_of_birth: profile.date_of_birth || "",
        gender: profile.gender || "",
        city: profile.city || "",
        state: profile.state || "",
        address: profile.address || "",
        pincode: profile.pincode || "",
        biometric_code: profile.biometric_code || "",
        role: userRole?.roles?.name || "",
        company_email: employee?.company_email || "",
        is_active: profile.is_active
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch user details",
        variant: "destructive",
      })
      navigate('/users')
    } finally {
      setLoading(false)
    }
  }

  // Validate all fields before submission
  const validateForm = () => {
    const newErrors = {
      first_name: formData.first_name ? "" : "First name is required",
      last_name: formData.last_name ? "" : "Last name is required",
      personal_email: "",
      phone: "",
      role: formData.role ? "" : "Role is required"
    }
    
    // Email validation
    if (!formData.personal_email) {
      newErrors.personal_email = "Email is required"
    } else if (!validateEmail(formData.personal_email)) {
      newErrors.personal_email = "Please enter a valid email"
    }
    
    // Phone validation (only if provided)
    if (formData.phone && !validatePhone(formData.phone)) {
      newErrors.phone = "Please enter a valid 10-digit phone number"
    }
    
    setErrors({
      ...newErrors,
      biometric_code: "" // Include the missing biometric_code property
    })
    
    // Check if there are any errors
    return !Object.values(newErrors).some(error => error !== "")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form before submission
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      })
      return
    }
    
    setSaving(true)

    try {
      // Update user profile
      const profileData: any = {
        first_name: formData.first_name,
        middle_name: formData.middle_name || null,
        last_name: formData.last_name,
        personal_email: formData.personal_email,
        phone: formData.phone || null,
        date_of_birth: formData.date_of_birth || null,
        city: formData.city || null,
        state: formData.state || null,
        address: formData.address || null,
        pincode: formData.pincode || null,
        biometric_code: formData.biometric_code || null,
        is_active: formData.is_active,
        updated_at: new Date().toISOString()
      }

      if (formData.gender && ['male', 'female', 'other'].includes(formData.gender)) {
        profileData.gender = formData.gender
      }

      const { error: profileError } = await supabase
        .from('user_profiles')
        .update(profileData)
        .eq('id', id)

      if (profileError) throw profileError

      // Update employee company email if exists
      if (formData.company_email) {
        const { error: employeeError } = await supabase
          .from('employees')
          .update({ company_email: formData.company_email })
          .eq('user_id', id)

        if (employeeError) console.warn('Employee update failed:', employeeError)
      }

      toast({
        title: "Success",
        description: "User updated successfully",
      })
      navigate('/users')
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading...</div>
        </div>
      </Layout>
    )
  }
   if (permissionsLoading) {
    return <Layout><div>Loading...</div></Layout>;
  }
  if (!hasPermission('users', 'edit')) {
  return (
    <Layout>
      <div className="p-8 text-center text-red-600">
        <h1>Access Denied</h1>
        <p>You do not have permission to edit users.</p>
      </div>
    </Layout>
  );
}

  return (
    <Layout>
      <div className="mx-auto space-y-1 p-2 md:p-2">
        <div className="flex flex-col sm:flex-row bg-[#4A9782] justify-between items-start sm:items-center gap-4  dark:bg-gray-800 rounded-lg p-6 text-white shadow-sm">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/users')} className="hidden sm:flex">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl md:text-xl font-bold tracking-tight">Edit User</h1>
              <p className="text-muted-foreground mt-1 text-white">Update user information</p>
            </div>
          </div>
          <Button variant="outline" onClick={() => navigate('/users')} className="sm:hidden w-full">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
            {/* Personal Information */}
            <Card className="border-0 shadow-md">
              <CardHeader className="border-b bg-muted/30 rounded-t-lg">
                <CardTitle className="text-lg font-semibold">Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="first_name" className="text-sm font-medium">
                      First Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="first_name"
                      value={formData.first_name}
                      onChange={(e) => handleInputChange('first_name', e.target.value)}
                      className={`mt-1.5 ${errors.first_name ? 'border-red-500' : ''}`}
                      placeholder="Enter first name"
                      required
                    />
                    {errors.first_name && (
                      <p className="text-red-500 text-xs mt-1 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.first_name}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="middle_name">Middle Name</Label>
                    <Input
                      id="middle_name"
                      value={formData.middle_name}
                      onChange={(e) => handleInputChange('middle_name', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="last_name" className="text-sm font-medium">
                      Last Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="last_name"
                      value={formData.last_name}
                      onChange={(e) => handleInputChange('last_name', e.target.value)}
                      className={`mt-1.5 ${errors.last_name ? 'border-red-500' : ''}`}
                      placeholder="Enter last name"
                      required
                    />
                    {errors.last_name && (
                      <p className="text-red-500 text-xs mt-1 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.last_name}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="personal_email" className="text-sm font-medium">
                      Personal Email <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="personal_email"
                      type="email"
                      value={formData.personal_email}
                      onChange={(e) => handleInputChange('personal_email', e.target.value)}
                      className={`mt-1.5 ${errors.personal_email ? 'border-red-500' : ''}`}
                      placeholder="Enter personal email"
                      required
                    />
                    {errors.personal_email && (
                      <p className="text-red-500 text-xs mt-1 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.personal_email}
                      </p>
                    )}
                    {isCheckingEmail && (
                      <p className="text-gray-500 text-xs mt-1">Checking email...</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className={`${errors.phone ? 'border-red-500' : ''}`}
                      placeholder="Enter 10-digit phone number"
                    />
                    {errors.phone && (
                      <p className="text-red-500 text-xs mt-1 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.phone}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="date_of_birth">Date of Birth</Label>
                    <Input
                      id="date_of_birth"
                      type="date"
                      value={formData.date_of_birth}
                      onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="gender">Gender</Label>
                    <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Address & Account Details */}
            <Card className="border-0 shadow-md">
              <CardHeader className="border-b bg-muted/30 rounded-t-lg">
                <CardTitle className="text-lg font-semibold">Address & Account Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div>
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={formData.state}
                      onChange={(e) => handleInputChange('state', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pincode">Pincode</Label>
                    <Input
                      id="pincode"
                      value={formData.pincode}
                      onChange={(e) => handleInputChange('pincode', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="biometric_code">Biometric Code</Label>
                    <Input
                      id="biometric_code"
                      value={formData.biometric_code}
                      onChange={(e) => handleInputChange('biometric_code', e.target.value)}
                      className={`${errors.biometric_code ? 'border-red-500' : ''}`}
                      placeholder="Enter biometric code"
                    />
                    {errors.biometric_code && (
                      <p className="text-red-500 text-xs mt-1 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.biometric_code}
                      </p>
                    )}
                    {isCheckingBiometricCode && (
                      <p className="text-gray-500 text-xs mt-1">Checking biometric code...</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="role">Role</Label>
                  <Input
                    id="role"
                    value={formData.role}
                    disabled
                    className="bg-gray-100"
                  />
                  <p className="text-xs text-gray-500 mt-1">Role cannot be changed after creation</p>
                </div>

                {formData.company_email && (
                  <div>
                    <Label htmlFor="company_email">Company Email</Label>
                    <Input
                      id="company_email"
                      type="email"
                      value={formData.company_email}
                      onChange={(e) => handleInputChange('company_email', e.target.value)}
                    />
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="border-0 shadow-md">
            <CardContent className="flex flex-col sm:flex-row justify-end gap-4 py-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/users')}
                className="w-full sm:w-auto order-1 sm:order-none"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={saving}
                className="w-full sm:w-auto bg-primary hover:bg-primary/90"
              >
                {saving ? "Updating..." : "Update User"}
              </Button>
            </CardContent>
          </Card>
        </form>
      </div>
    </Layout>
  )
}