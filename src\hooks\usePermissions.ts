import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/integrations/supabase/client'

interface Permission {
  id: string
  name: string
  module: string
  can_view: boolean
  can_add: boolean
  can_edit: boolean
  can_delete: boolean
}

export const usePermissions = () => {
  const { user, userRole } = useAuth()
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user && userRole) {
      console.log('Fetching permissions for role:', userRole)
      fetchPermissions()
    }
  }, [user, userRole])

  const fetchPermissions = async () => {
    if (!user || !userRole) return;
    try {
   const { data: role } = await supabase
        .from('roles')
        .select('id')
        .eq('name', userRole)
        .single()

      let rolePermissionsList: Permission[] = [];
      if (role) {
        const { data: rolePerms } = await supabase
          .from('role_permissions')
          .select('permissions(*)')
          .eq('role_id', role.id)
          .eq('is_active', true);
        
        rolePermissionsList = rolePerms?.map(rp => rp.permissions).filter(Boolean) || [];
      }

      // 2. Fetch User-Specific Permissions
      const { data: userPerms } = await supabase
        .from('user_permissions')
        .select('permissions(*)')
        .eq('user_id', user.id)
        .eq('is_active', true);

      const userPermissionsList: Permission[] = userPerms?.map(up => up.permissions).filter(Boolean) || [];

      // 3. Combine both lists and remove duplicates
      //    We use a Map to ensure that if a permission ID is in both lists, it's only added once.
      const combinedPermissions = new Map<string, Permission>();

      rolePermissionsList.forEach(p => combinedPermissions.set(p.id, p));
      userPermissionsList.forEach(p => combinedPermissions.set(p.id, p)); // User permissions will overwrite role permissions if they share the same permission ID

      const finalPermissions = Array.from(combinedPermissions.values());
      
      console.log('Final combined permissions:', finalPermissions);
     setPermissions(finalPermissions);

    } catch (error) {
      console.error('Error fetching permissions:', error)
    } finally {
      setLoading(false)
    }
  }

  const hasPermission = (module: string, action: 'view' | 'add' | 'edit' | 'delete') => {
    const modulePermissions = permissions.filter(p => p.module === module);
   if (modulePermissions.length === 0) {
      return false;
    }

    
   return modulePermissions.some(p => {
      switch (action) {
        case 'view': return p.can_view;
        case 'add': return p.can_add;
        case 'edit': return p.can_edit;
        case 'delete': return p.can_delete;
        default: return false;
      }
    });
  }

   const canAccess = (module: string) => {
    // This function now works correctly with the new hasPermission logic
    return hasPermission(module, 'view');
  };


  return {
    permissions,
    loading,
    hasPermission,
    canAccess,
    userRole
  };
}