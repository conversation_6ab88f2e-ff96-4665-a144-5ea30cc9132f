import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { Layout } from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { usePermissions } from "@/hooks/usePermissions";

interface UserProfile {
  id: string;
  first_name: string;
  last_name: string;
}

interface Permission {
  id: string;
  name: string;
  module: string;
}

export default function UserPermissions() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [userPermissions, setUserPermissions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedUserId) {
      fetchUserPermissions(selectedUserId);
    } else {
      setUserPermissions([]);
    }
  }, [selectedUserId]);

  const fetchInitialData = async () => {
    try {
      const [usersRes, permissionsRes] = await Promise.all([
        supabase.from('user_profiles').select('id, first_name, last_name').eq('is_deleted', false).order('first_name'),
        supabase.from('permissions').select('id, name, module').eq('is_deleted', false).order('module').order('name')
      ]);

      if (usersRes.error) throw usersRes.error;
      if (permissionsRes.error) throw permissionsRes.error;

      setUsers(usersRes.data || []);
      setPermissions(permissionsRes.data || []);
    } catch (error) {
      console.error('Error fetching initial data:', error);
      toast({ title: "Error", description: "Failed to load users and permissions.", variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

  const fetchUserPermissions = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_permissions')
        .select('permission_id')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) throw error;
      setUserPermissions(data?.map(p => p.permission_id) || []);
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      toast({ title: "Error", description: "Failed to load permissions for the selected user.", variant: "destructive" });
    }
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    setUserPermissions(prev =>
      checked ? [...prev, permissionId] : prev.filter(id => id !== permissionId)
    );
  };

  const handleSavePermissions = async () => {
    if (!selectedUserId) {
      toast({ title: "Warning", description: "Please select a user first.", variant: "default" });
      return;
    }
    setSaving(true);
    try {
      const { data: { user: currentUser } } = await supabase.auth.getUser();

      // Deactivate all current permissions for the user
      await supabase
        .from('user_permissions')
        .update({ is_active: false, updated_by: currentUser?.id, updated_at: new Date().toISOString() })
        .eq('user_id', selectedUserId);

      // Upsert the new set of permissions
      if (userPermissions.length > 0) {
        const permissionsToUpsert = userPermissions.map(permissionId => ({
          user_id: selectedUserId,
          permission_id: permissionId,
          is_active: true,
          created_by: currentUser?.id,
          updated_by: currentUser?.id,
        }));

        const { error } = await supabase
          .from('user_permissions')
          .upsert(permissionsToUpsert, { onConflict: 'user_id,permission_id' });

        if (error) throw error;
      }

      toast({ title: "Success", description: "User permissions have been updated." });
    } catch (error: any) {
      console.error('Error saving user permissions:', error);
      toast({ title: "Error", description: error.message || "Failed to save permissions.", variant: "destructive" });
    } finally {
      setSaving(false);
    }
  };

  const groupedPermissions = permissions.reduce((acc, p) => {
    acc[p.module] = acc[p.module] || [];
    acc[p.module].push(p);
    return acc;
  }, {} as Record<string, Permission[]>);

  if (loading || permissionsLoading) {
    return <Layout><div>Loading...</div></Layout>;
  }

  if (!canAccess('user_permissions_management')) {
    return (
      <Layout>
        <div className="p-8 text-center text-red-600">
          <h2>Access Denied</h2>
          <p>You do not have permission to manage user-specific permissions.</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Permissions</h1>
          <p className="text-muted-foreground">Assign specific permissions to individual users, overriding their role.</p>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Select a User</CardTitle>
            <CardDescription>Choose a user to view or modify their specific permissions.</CardDescription>
          </CardHeader>
          <CardContent>
            <Select value={selectedUserId} onValueChange={setSelectedUserId}>
              <SelectTrigger className="w-full md:w-1/2">
                <SelectValue placeholder="Select a user..." />
              </SelectTrigger>
              <SelectContent>
                {users.map(user => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.first_name} {user.last_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {selectedUserId && (
          <Card>
            <CardHeader>
              <CardTitle>Assign Permissions</CardTitle>
              <CardDescription>
                Select the permissions for {users.find(u => u.id === selectedUserId)?.first_name}. These will be added on top of their role permissions.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 max-h-[60vh] overflow-y-auto p-6">
              {Object.entries(groupedPermissions).map(([module, perms]) => (
                <div key={module}>
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wider mb-3 border-b pb-2">
                    {module.replace(/_/g, ' ')}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {perms.map(permission => (
                      <div key={permission.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={permission.id}
                          checked={userPermissions.includes(permission.id)}
                          onCheckedChange={(checked) => handlePermissionChange(permission.id, checked === true)}
                          disabled={!hasPermission('user_permissions_management', 'edit')}
                        />
                        <Label htmlFor={permission.id} className="text-sm font-normal">
                          {permission.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </CardContent>
            <div className="p-6 border-t flex justify-end">
              {hasPermission('user_permissions_management', 'edit') && (
                <Button onClick={handleSavePermissions} disabled={saving}>
                  {saving ? "Saving..." : "Save Permissions"}
                </Button>
              )}
            </div>
          </Card>
        )}
      </div>
    </Layout>
  );
}
