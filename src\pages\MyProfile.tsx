import { useState, useEffect } from "react"
import { User, FileText, Calendar, CreditCard, Clock, Building, Edit } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/AuthContext"
import { supabase } from "@/integrations/supabase/client"
import { format } from "date-fns"

export default function MyProfile() {
  const { user } = useAuth()
  const [employee, setEmployee] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchEmployeeProfile()
    }
  }, [user])

  const fetchEmployeeProfile = async () => {
    try {
      console.log('Fetching profile for user:', user?.id)

      // Get employee data
      const { data: employeeData, error: empError } = await supabase
        .from('employees')
        .select('*')
        .eq('user_id', user?.id)
        .single()

      console.log('Employee data:', employeeData, 'Error:', empError)

      if (empError) {
        console.error('Employee query error:', empError)
        setEmployee(null)
        return
      }

      // Get user profile using the user_id from employee data
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', employeeData.user_id)
        .single()

      console.log('Profile data:', profileData, 'Error:', profileError)

      // Get department if exists
      let deptData = null
      if (employeeData.department_id) {
        const { data: dept } = await supabase
          .from('departments')
          .select('name, code')
          .eq('id', employeeData.department_id)
          .single()
        deptData = dept
      }

      // Get designation if exists
      let desigData = null
      if (employeeData.designation_id) {
        const { data: desig } = await supabase
          .from('designations')
          .select('title, code')
          .eq('id', employeeData.designation_id)
          .single()
        desigData = desig
      }

      // Get bank details
      const { data: bankData } = await supabase
        .from('employee_bank_details')
        .select('*')
        .eq('employee_id', employeeData.id)

      // Get documents
      const { data: docsData } = await supabase
        .from('employee_documents')
        .select('*')
        .eq('employee_id', employeeData.id)

      // Get active contracts
      const { data: contractsData } = await supabase
        .from('contracts')
        .select(`
          *,
          contract_types(name, code),
          contract_groups(id, name, status),
          employee_salary_components(
            value,
            salary_components(name, component_type)
          )
        `)
        .eq('employee_id', employeeData.id)
        .eq('status', 'active')

      // Get employee shifts
      const { data: shiftsData } = await supabase.from('employee_shifts').select(`*, shifts(*), work_weeks(*)`).eq('employee_id', employeeData.id).eq('is_active', true)


      console.log('All data fetched:', {
        employee: employeeData,
        profile: profileData,
        department: deptData,
        designation: desigData,
        bank: bankData,
        documents: docsData,
        contracts: contractsData,
        shifts: shiftsData
      })

      // Group contracts by contract_group
      const groupedContracts = contractsData?.reduce((acc: any, contract: any) => {
        const groupId = contract.contract_group_id
        if (!acc[groupId]) {
          acc[groupId] = {
            id: contract.contract_groups?.id,
            name: contract.contract_groups?.name,
            status: contract.contract_groups?.status,
            contracts: []
          }
        }
        acc[groupId].contracts.push(contract)
        return acc
      }, {}) || {}

      setEmployee({
        ...employeeData,
        user_profiles: profileData,
        departments: deptData,
        designations: desigData,
        employee_bank_details: bankData || [],
        employee_documents: docsData || [],
        employee_shifts: shiftsData || [],
        contract_groups: Object.values(groupedContracts)
      })

    } catch (error) {
      console.error('Error fetching employee profile:', error)
      setEmployee(null)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading profile...</div>
        </div>
      </Layout>
    )
  }

  if (!employee) {
    return (
      <Layout>
        <div className="text-center py-8">
          <p>Profile not found</p>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="p-6 space-y-6">
        {/* Profile Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
                <User className="h-10 w-10" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {employee.user_profiles?.first_name || 'User'} {employee.user_profiles?.last_name || ''}
                </h1>
                <p className="text-xl opacity-90">
                  {employee.designations?.title} • {employee.departments?.name}
                </p>
                <p className="opacity-75">
                  Employee ID: {employee.employee_code}
                </p>
              </div>
            </div>
            <Button variant="secondary" className="text-gray-900">
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          </div>
        </div>

        {/* Profile Content */}
        <Tabs defaultValue="personal" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="personal">Personal</TabsTrigger>
            <TabsTrigger value="work">Work Details</TabsTrigger>
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
            <TabsTrigger value="shifts">Shifts</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>

          <TabsContent value="personal" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">First Name</label>
                      <p className="font-medium">{employee.user_profiles?.first_name || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Last Name</label>
                      <p className="font-medium">{employee.user_profiles?.last_name || 'N/A'}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Email</label>
                    <p className="font-medium">{employee.user_profiles?.personal_email || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Phone</label>
                    <p className="font-medium">{employee.user_profiles?.phone || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Date of Birth</label>
                    <p className="font-medium">
                      {employee.user_profiles?.date_of_birth ?
                        format(new Date(employee.user_profiles.date_of_birth), 'PPP') : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Gender</label>
                    <p className="font-medium">{employee.user_profiles?.gender || 'N/A'}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Address Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Address</label>
                    <p className="font-medium">{employee.user_profiles?.address || 'N/A'}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">City</label>
                      <p className="font-medium">{employee.user_profiles?.city || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">State</label>
                      <p className="font-medium">{employee.user_profiles?.state || 'N/A'}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Pincode</label>
                    <p className="font-medium">{employee.user_profiles?.pincode || 'N/A'}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="work" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Employment Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Employee Code</label>
                    <p className="font-medium">{employee.employee_code}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Company Email</label>
                    <p className="font-medium">{employee.company_email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Department</label>
                    <p className="font-medium">{employee.departments?.name} ({employee.departments?.code})</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Designation</label>
                    <p className="font-medium">{employee.designations?.title} ({employee.designations?.code})</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Hire Date</label>
                    <p className="font-medium">{format(new Date(employee.hire_date), 'PPP')}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Employment Status</label>
                    <Badge variant={employee.employment_status === 'active' ? 'default' : 'secondary'}>
                      {employee.employment_status}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Banking Information</CardTitle>
                </CardHeader>
                <CardContent>
                  {employee.employee_bank_details && employee.employee_bank_details.length > 0 ? (
                    <div className="space-y-4">
                      {employee.employee_bank_details.map((bank: any, index: number) => (
                        <div key={index} className="border rounded p-3">
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <label className="text-sm font-medium text-gray-600">Bank Name</label>
                              <p className="font-medium">{bank.bank_name}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-600">Account Holder</label>
                              <p className="font-medium">{bank.account_holder_name}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-600">Account Number</label>
                              <p className="font-medium font-mono">****{bank.account_number.slice(-4)}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-600">IFSC Code</label>
                              <p className="font-medium font-mono">{bank.ifsc_code}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">No banking information available</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="contracts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Employment Contracts</CardTitle>
              </CardHeader>
              <CardContent>
                {employee.contract_groups && employee.contract_groups.length > 0 ? (
                  <div className="space-y-6">
                    {employee.contract_groups.map((group: any) => (
                      <div key={group.id} className="border-2 border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-lg font-semibold">{group.name || 'Contract Group'}</h3>
                          <Badge variant={group.status === 'active' ? 'default' : 'secondary'}>
                            {group.status || 'unknown'}
                          </Badge>
                        </div>

                        {group.contracts?.map((contract: any) => (
                          <div key={contract.id} className="bg-gray-50 rounded p-4 mb-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div>
                                <label className="text-sm font-medium text-gray-600">Contract Type</label>
                                <p className="font-medium">{contract.contract_types?.name || 'N/A'}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-600">Period</label>
                                <p className="font-medium">
                                  {format(new Date(contract.start_date), 'MMM yyyy')} -
                                  {contract.end_date ? format(new Date(contract.end_date), 'MMM yyyy') : 'Ongoing'}
                                </p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-600">Basic Salary</label>
                                <p className="font-medium text-green-600">₹{contract.basic_salary.toLocaleString()}</p>
                              </div>
                            </div>

                            {contract.employee_salary_components && contract.employee_salary_components.length > 0 && (
                              <div className="mt-4 pt-3 border-t">
                                <label className="text-sm font-medium text-gray-600 block mb-2">Salary Components</label>
                                <div className="grid grid-cols-2 gap-2">
                                  {contract.employee_salary_components.map((sc: any, index: number) => (
                                    <div key={index} className="flex justify-between p-2 bg-white rounded">
                                      <span className="text-sm">{sc.salary_components?.name || 'N/A'}</span>
                                      <span className="text-sm font-medium">₹{sc.value?.toLocaleString() || '0'}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No contracts found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="shifts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Work Schedule</CardTitle>
              </CardHeader>
              <CardContent>
                {employee.employee_shifts && employee.employee_shifts.length > 0 ? (
                  <div className="space-y-4">
                    {employee.employee_shifts.map((shift: any, index: number) => (
                      <div key={index} className="border rounded p-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-600">Shift Name</label>
                            <p className="font-medium">{shift.shifts?.name || 'N/A'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Timing</label>
                            <p className="font-medium">
                              {shift.shifts?.start_time || 'N/A'} - {shift.shifts?.end_time || 'N/A'}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Work Week</label>
                            <p className="font-medium">{shift.work_weeks?.name || 'N/A'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-600">Work Days</label>
                            <p className="font-medium">{shift.work_weeks?.days || 'N/A'}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No shift information available</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>My Documents</CardTitle>
              </CardHeader>
              <CardContent>
                {employee.employee_documents && employee.employee_documents.length > 0 ? (
                  <div className="space-y-3">
                    {employee.employee_documents.map((doc: any) => (
                      <div key={doc.id} className="flex items-center justify-between border rounded p-3">
                        <div>
                          <p className="font-medium">{doc.document_type}</p>
                          <p className="text-sm text-gray-600">
                            Uploaded: {format(new Date(doc.uploaded_at), 'PPP')}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={doc.is_verified ? 'default' : 'secondary'}>
                            {doc.is_verified ? 'Verified' : 'Pending'}
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(doc.file_url, '_blank')}
                          >
                            View
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No documents found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  )
}