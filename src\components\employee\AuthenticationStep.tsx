import { useState, useEffect } from "react"
import { Calendar, Eye, EyeOff } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { supabase } from "@/integrations/supabase/client"

interface AuthDetailsData {
  company_email: string
  password: string
  employee_code: string
  hire_date: Date | null
  department_id: string
  designation_id: string
}

interface AuthenticationStepProps {
  data: AuthDetailsData
  onChange: (data: Partial<AuthDetailsData>) => void
}

interface Department {
  id: string
  name: string
  code: string
}

interface Designation {
  id: string
  title: string
  code: string
  department_id: string
}

export default function AuthenticationStep({ data, onChange }: AuthenticationStepProps) {
  const [departments, setDepartments] = useState<Department[]>([])
  const [designations, setDesignations] = useState<Designation[]>([])
  const [filteredDesignations, setFilteredDesignations] = useState<Designation[]>([])
  const [showPassword, setShowPassword] = useState(false) // Add this line

  useEffect(() => {
    fetchDepartments()
    fetchDesignations()
  }, [])

  useEffect(() => {
    if (data.department_id) {
      const filtered = designations.filter(d => d.department_id === data.department_id)
      setFilteredDesignations(filtered)
      // Reset designation if it doesn't belong to selected department
      if (data.designation_id && !filtered.find(d => d.id === data.designation_id)) {
        onChange({ designation_id: "" })
      }
    } else {
      setFilteredDesignations([])
    }
  }, [data.department_id, designations])

  const fetchDepartments = async () => {
    try {
      const { data: deptData, error } = await supabase
        .from('departments')
        .select('id, name, code')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('name')

      if (error) throw error
      setDepartments(deptData || [])
    } catch (error) {
      console.error('Error fetching departments:', error)
    }
  }

  const fetchDesignations = async () => {
    try {
      const { data: desigData, error } = await supabase
        .from('designations')
        .select('id, title, code, department_id')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('title')

      if (error) throw error
      setDesignations(desigData || [])
    } catch (error) {
      console.error('Error fetching designations:', error)
    }
  }

  const handleInputChange = (field: keyof AuthDetailsData, value: any) => {
    onChange({ [field]: value })
  }

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@#$'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    handleInputChange('password', password)
  }

  return (
    <div className="space-y-6">
      {/* Login Credentials */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Login Credentials</h3>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="company_email" className="text-sm font-medium">
              Company Email <span className="text-red-500">*</span>
            </Label>
            <Input
              id="company_email"
              type="email"
              value={data.company_email}
              onChange={(e) => handleInputChange('company_email', e.target.value)}
              placeholder="<EMAIL>"
              className="mt-1.5"
              required
            />
          </div>

          <div>
            <Label htmlFor="password" className="text-sm font-medium">
              Password <span className="text-red-500">*</span>
            </Label>
            <div className="flex gap-2 mt-1.5">
              <div className="relative flex-1">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={data.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="Enter password"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={generatePassword}
              >
                Generate
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Employee Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Employee Details</h3>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="employee_code">Employee Code</Label>
            <Input
              id="employee_code"
              value={data.employee_code}
              onChange={(e) => handleInputChange('employee_code', e.target.value)}
              placeholder="EMP001"
              readOnly
            />
          </div>

          <div>
            <Label className="text-sm font-medium">
              Hire Date <span className="text-red-500">*</span>
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal mt-1.5",
                    !data.hire_date && "text-muted-foreground"
                  )}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {data.hire_date ? format(data.hire_date, "PPP") : "Select hire date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  mode="single"
                  selected={data.hire_date || undefined}
                  onSelect={(date) => handleInputChange('hire_date', date)}
                  disabled={(date) => date > new Date()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>

      {/* Department & Designation */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Department & Role</h3>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="department">Department</Label>
            <Select
              value={data.department_id}
              onValueChange={(value) => handleInputChange('department_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map((dept) => (
                  <SelectItem key={dept.id} value={dept.id}>
                    {dept.name} {dept.code && `(${dept.code})`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="designation">Designation</Label>
            <Select
              value={data.designation_id}
              onValueChange={(value) => handleInputChange('designation_id', value)}
              disabled={!data.department_id}
            >
              <SelectTrigger>
                <SelectValue placeholder={
                  data.department_id ? "Select designation" : "Select department first"
                } />
              </SelectTrigger>
              <SelectContent>
                {filteredDesignations.map((desig) => (
                  <SelectItem key={desig.id} value={desig.id}>
                    {desig.title} {desig.code && `(${desig.code})`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  )
}