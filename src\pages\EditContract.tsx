import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { ArrowLeft, Calendar, User, FileText, DollarSign, Building, Clock, CheckSquare, AlertCircle, Save, X, Settings, Users, Briefcase, CreditCard } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { usePermissions } from "@/hooks/usePermissions";
// Interface definitions
interface Employee {
  id: string
  employee_code: string
  user_id: string
  user_profiles: {
    first_name: string
    last_name: string
  }
}

interface ContractType {
  id: string
  name: string
}

interface ContractGroup {
  id: string
  name: string
}

interface ContractTemplate {
  id: string
  name: string
}

interface Holiday {
  id: string
  name: string
  type: string
}

interface LeaveType {
  id: string
  name: string
  code: string
}

interface SalaryComponent {
  id: string
  name: string
  code: string
  component_type: 'earning' | 'deduction'
  calculation_type: string
  default_value?: number
  is_taxable: boolean
  description?: string
}

// Main Component
export default function EditContract() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const { id } = useParams<{ id: string }>()

  // State Management
  const [loading, setLoading] = useState(true)
  const [contract, setContract] = useState<any>(null)
  const [employees, setEmployees] = useState<Employee[]>([])
  const [contractTypes, setContractTypes] = useState<ContractType[]>([])
  const [contractGroups, setContractGroups] = useState<ContractGroup[]>([])
  const [contractTemplates, setContractTemplates] = useState<ContractTemplate[]>([])
  const [holidays, setHolidays] = useState<Holiday[]>([])
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([])
  const [salaryComponents, setSalaryComponents] = useState<SalaryComponent[]>([])
  const [selectedSalaryComponents, setSelectedSalaryComponents] = useState<any[]>([])
  const [selectedHolidays, setSelectedHolidays] = useState<string[]>([])
  const [selectedLeaves, setSelectedLeaves] = useState<any[]>([])
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
   const { hasPermission, loading: permissionsLoading } = usePermissions();

  const [formData, setFormData] = useState({
    employee_id: "",
    contract_group_id: "",
    contract_type_id: "",
    contract_template_id: "",
    overtime_allowed: false,
    overtime_rate: "",
    probation_period: "",
    notice_period: "",
    status: "draft" as "draft" | "active" | "expired" | "terminated"
  })

  // Fetch data on component mount
  useEffect(() => {
    if (id) {
      fetchContractData()
    }
  }, [id])

  // Data fetching logic
  const fetchContractData = async () => {
    setLoading(true)
    try {
      const { data: contractData, error } = await supabase.from('contracts').select('*').eq('id', id).single()
      if (error) throw error

      setContract(contractData)
      setStartDate(new Date(contractData.start_date))
      setEndDate(contractData.end_date ? new Date(contractData.end_date) : undefined)

      setFormData({
        employee_id: contractData.employee_id,
        contract_group_id: contractData.contract_group_id || "",
        contract_type_id: contractData.contract_type_id,
        contract_template_id: contractData.contract_template_id || "",
        overtime_allowed: contractData.overtime_allowed,
        overtime_rate: contractData.overtime_rate?.toString() || "",
        probation_period: contractData.probation_period?.toString() || "",
        notice_period: contractData.notice_period?.toString() || "",
        status: contractData.status
      })

      // Fetch related data in parallel
      const [employeesRes, typesRes, groupsRes, salaryCompRes, holidaysRes, leavesRes, templatesRes, empSalCompRes, contractHolidaysRes, contractLeavesRes] = await Promise.all([
        supabase.from('employees').select('id, employee_code, user_id, user_profiles(first_name, last_name)'),
        supabase.from('contract_types').select('id, name'),
        supabase.from('contract_groups').select('id, name'),
        supabase.from('salary_components').select('*'),
        supabase.from('holidays').select('id, name, type'),
        supabase.from('leave_types').select('id, name, code'),
        supabase.from('contract_templates').select('id, name').eq('contract_type_id', contractData.contract_type_id),
        supabase.from('employee_salary_components').select('*, salary_components(*)').eq('contract_id', id).eq('is_deleted', false),
        supabase.from('contract_holidays').select('holiday_id').eq('contract_id', id),
        supabase.from('contract_leaves').select('*').eq('contract_id', id)
      ])

      setEmployees(employeesRes.data as any || [])
      setContractTypes(typesRes.data || [])
      setContractGroups(groupsRes.data || [])
      setSalaryComponents(salaryCompRes.data || [])
      setHolidays(holidaysRes.data || [])
      setLeaveTypes(leavesRes.data || [])
      setContractTemplates(templatesRes.data || [])

      setSelectedSalaryComponents(empSalCompRes.data?.map(esc => ({ ...esc, component: esc.salary_components })) || [])
      setSelectedHolidays(contractHolidaysRes.data?.map(h => h.holiday_id) || [])
      setSelectedLeaves(contractLeavesRes.data || [])

    } catch (error) {
      console.error('Error fetching contract data:', error)
      toast({ title: "Error", description: "Failed to fetch contract data.", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  // Calculate total salary
  const calculateTotalSalary = () => {
    const earnings = selectedSalaryComponents.filter(sc => sc.component?.component_type === 'earning').reduce((sum, sc) => sum + Number(sc.value), 0)
    const deductions = selectedSalaryComponents.filter(sc => sc.component?.component_type === 'deduction').reduce((sum, sc) => sum + Number(sc.value), 0)
    return earnings - deductions
  }

  // Handle contract type change to fetch relevant templates
  const handleContractTypeChange = async (contractTypeId: string) => {
    setFormData(prev => ({ ...prev, contract_type_id: contractTypeId, contract_template_id: '' }))
    const { data } = await supabase.from('contract_templates').select('*').eq('contract_type_id', contractTypeId)
    setContractTemplates(data || [])
  }

  // Form submission handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!startDate) {
      toast({ title: "Error", description: "Start date is required.", variant: "destructive" })
      return
    }
    setLoading(true)
    try {
      const totalSalary = calculateTotalSalary()
      const { error: contractError } = await supabase.from('contracts').update({
        ...formData,
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate ? endDate.toISOString().split('T')[0] : null,
        basic_salary: totalSalary,
        overtime_rate: formData.overtime_rate ? parseFloat(formData.overtime_rate) : null,
        probation_period: formData.probation_period ? parseInt(formData.probation_period) : null,
        notice_period: formData.notice_period ? parseInt(formData.notice_period) : null,
      }).eq('id', id)
      if (contractError) throw contractError

      // Update related tables
      await supabase.from('employee_salary_components').delete().eq('contract_id', id)
      const salaryInserts = selectedSalaryComponents.map(sc => ({ employee_id: formData.employee_id, contract_id: id, salary_component_id: sc.salary_component_id, value: sc.value, effective_from: startDate.toISOString().split('T')[0], is_active: true }))
      await supabase.from('employee_salary_components').insert(salaryInserts)

      await supabase.from('contract_holidays').delete().eq('contract_id', id)
      const holidayInserts = selectedHolidays.map(hid => ({ contract_id: id, holiday_id: hid, is_applicable: true }))
      await supabase.from('contract_holidays').insert(holidayInserts)

      await supabase.from('contract_leaves').delete().eq('contract_id', id)
      const leaveInserts = selectedLeaves.map(l => ({ ...l, id: undefined, contract_id: id }))
      await supabase.from('contract_leaves').insert(leaveInserts)

      toast({ title: "Success", description: "Contract updated successfully." })
      navigate('/contracts')
    } catch (error) {
      console.error('Error updating contract:', error)
      toast({ title: "Error", description: "Failed to update contract.", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <Layout><div className="flex justify-center items-center h-screen">Loading...</div></Layout>
  }

  if (!contract) {
    return <Layout><div className="flex justify-center items-center h-screen">Contract not found.</div></Layout>
  }

  const currentEmployee = employees.find(emp => emp.id === formData.employee_id);
if (permissionsLoading) {return <Layout><div>Loading...</div></Layout>;}

  // 👇 ADD THIS PAGE GUARD 👇
  if (!hasPermission('contracts', 'edit')) {
    return (
      <Layout>
        <div className="p-8 text-center text-red-600">
          <h2>Access Denied</h2>
          <p>You do not have permission to edit contracts.</p>
        </div>
      </Layout>
    );
  }
  return (
    <Layout>
      <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 min-h-screen">
        <div className=" mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button variant="ghost" size="icon" onClick={() => navigate('/contracts')}><ArrowLeft className="h-5 w-5" /></Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Edit Contract</h1>
              <p className="text-sm text-gray-600">Update the terms for {currentEmployee?.user_profiles?.first_name} {currentEmployee?.user_profiles?.last_name}.</p>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column */}
              <div className="lg:col-span-2 space-y-8">
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                    <CardDescription>Employee and group are locked. You can update the contract type.</CardDescription>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label>Employee (Locked)</Label>
                      <div className="h-10 border rounded-md px-3 bg-gray-100 flex items-center text-sm text-gray-700">
                        {currentEmployee?.user_profiles?.first_name} {currentEmployee?.user_profiles?.last_name}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Contract Group (Locked)</Label>
                      <div className="h-10 border rounded-md px-3 bg-gray-100 flex items-center text-sm text-gray-700">
                        {contractGroups.find(g => g.id === formData.contract_group_id)?.name || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contract_type">Contract Type *</Label>
                      <Select value={formData.contract_type_id} onValueChange={handleContractTypeChange}>
                        <SelectTrigger><SelectValue placeholder="Select type" /></SelectTrigger>
                        <SelectContent>{contractTypes.map(t => <SelectItem key={t.id} value={t.id}>{t.name}</SelectItem>)}</SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contract_template">Contract Template</Label>
                      <Select value={formData.contract_template_id} onValueChange={v => setFormData(p => ({ ...p, contract_template_id: v }))}>
                        <SelectTrigger><SelectValue placeholder="Select template" /></SelectTrigger>
                        <SelectContent>{contractTemplates.map(t => <SelectItem key={t.id} value={t.id}>{t.name}</SelectItem>)}</SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Timeline & Policies */}
                <Card>
                  <CardHeader>
                    <CardTitle>Timeline & Policies</CardTitle>
                    <CardDescription>Adjust the contract dates and policy terms.</CardDescription>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label>Start Date *</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className={cn("w-full justify-start text-left font-normal", !startDate && "text-muted-foreground")}>
                            <Calendar className="mr-2 h-4 w-4" />{startDate ? format(startDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0"><CalendarComponent mode="single" selected={startDate} onSelect={setStartDate} initialFocus /></PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label>End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className={cn("w-full justify-start text-left font-normal", !endDate && "text-muted-foreground")}>
                            <Calendar className="mr-2 h-4 w-4" />{endDate ? format(endDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0"><CalendarComponent mode="single" selected={endDate} onSelect={setEndDate} initialFocus /></PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="probation_period">Probation Period (months)</Label>
                      <Input id="probation_period" type="number" value={formData.probation_period} onChange={e => setFormData(p => ({ ...p, probation_period: e.target.value }))} placeholder="e.g., 3" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="notice_period">Notice Period (days)</Label>
                      <Input id="notice_period" type="number" value={formData.notice_period} onChange={e => setFormData(p => ({ ...p, notice_period: e.target.value }))} placeholder="e.g., 30" />
                    </div>
                  </CardContent>
                </Card>

                {/* Financial Details */}
                <Card>
                  <CardHeader>
                    <CardTitle>Financial Details</CardTitle>
                    <CardDescription>Adjust salary components and overtime settings.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <Label className="font-semibold">Salary Components *</Label>
                      <div className="max-h-80 overflow-y-auto space-y-3 border rounded-lg p-4 bg-gray-50">
                        {salaryComponents.map(c => {
                          const selected = selectedSalaryComponents.find(sc => sc.salary_component_id === c.id);
                          return (
                            <div key={c.id} className="p-3 bg-white rounded-md border">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <Checkbox id={`comp-${c.id}`} checked={!!selected} onCheckedChange={checked => {
                                    if (checked) setSelectedSalaryComponents([...selectedSalaryComponents, { salary_component_id: c.id, value: c.default_value || 0, component: c }]);
                                    else setSelectedSalaryComponents(selectedSalaryComponents.filter(sc => sc.salary_component_id !== c.id));
                                  }} />
                                  <div>
                                    <Label htmlFor={`comp-${c.id}`} className="font-medium">{c.name}</Label>
                                    <p className="text-xs text-gray-500">{c.code} - {c.component_type}</p>
                                  </div>
                                </div>
                                {selected && <div className="w-40"><Input type="number" step="0.01" min="0" value={selected.value} onChange={e => setSelectedSalaryComponents(selectedSalaryComponents.map(sc => sc.salary_component_id === c.id ? { ...sc, value: parseFloat(e.target.value) || 0 } : sc))} className="h-8 text-right" /></div>}
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2"><Checkbox id="overtime_allowed" checked={formData.overtime_allowed} onCheckedChange={c => setFormData(p => ({ ...p, overtime_allowed: c as boolean }))} /><Label htmlFor="overtime_allowed">Allow Overtime</Label></div>
                      {formData.overtime_allowed && <div className="space-y-2 pl-6"><Label htmlFor="overtime_rate">Overtime Rate (per hour)</Label><Input id="overtime_rate" type="number" value={formData.overtime_rate} onChange={e => setFormData(p => ({ ...p, overtime_rate: e.target.value }))} /></div>}
                    </div>
                  </CardContent>
                </Card>

                {/* Leave & Holiday Configuration */}
                <Card>
                  <CardHeader>
                    <CardTitle>Leave & Holiday Configuration</CardTitle>
                    <CardDescription>Manage leave policies and applicable holidays for this contract.</CardDescription>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Leave Types */}
                    <div className="space-y-4">
                      <Label className="font-semibold">Leave Types</Label>
                      <div className="max-h-80 overflow-y-auto space-y-3 border rounded-lg p-4 bg-gray-50">
                        {leaveTypes.map(lt => {
                          const selected = selectedLeaves.find(sl => sl.leave_type_id === lt.id);
                          return (
                            <div key={lt.id} className="p-3 bg-white rounded-md border">
                              <div className="flex items-center gap-3"><Checkbox id={`leave-${lt.id}`} checked={!!selected} onCheckedChange={c => {
                                if (c) setSelectedLeaves([...selectedLeaves, { leave_type_id: lt.id, days_allowed: 0, carry_forward: false, encashable: false, salary_payable: true }]);
                                else setSelectedLeaves(selectedLeaves.filter(sl => sl.leave_type_id !== lt.id));
                              }} /><Label htmlFor={`leave-${lt.id}`}>{lt.name}</Label></div>
                              {selected && <div className="grid grid-cols-2 gap-2 mt-2 pl-8 text-xs">
                                <div><Label>Days</Label><Input type="number" value={selected.days_allowed} onChange={e => setSelectedLeaves(selectedLeaves.map(l => l.leave_type_id === lt.id ? { ...l, days_allowed: parseInt(e.target.value) || 0 } : l))} className="h-8" /></div>
                                <div className="flex items-center gap-2 pt-4"><Checkbox checked={selected.carry_forward} onCheckedChange={c => setSelectedLeaves(selectedLeaves.map(l => l.leave_type_id === lt.id ? { ...l, carry_forward: c as boolean } : l))} /> <Label>Carry Fwd</Label></div>
                                <div className="flex items-center gap-2"><Checkbox checked={selected.encashable} onCheckedChange={c => setSelectedLeaves(selectedLeaves.map(l => l.leave_type_id === lt.id ? { ...l, encashable: c as boolean } : l))} /> <Label>Encashable</Label></div>
                                <div className="flex items-center gap-2"><Checkbox checked={selected.salary_payable} onCheckedChange={c => setSelectedLeaves(selectedLeaves.map(l => l.leave_type_id === lt.id ? { ...l, salary_payable: c as boolean } : l))} /> <Label>Payable</Label></div>
                              </div>}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                    {/* Holidays */}
                    <div className="space-y-4">
                      <Label className="font-semibold">Holidays</Label>
                      <div className="max-h-80 overflow-y-auto space-y-2 border rounded-lg p-4 bg-gray-50">
                        {holidays.map(h => <div key={h.id} className="flex items-center gap-3 p-2 bg-white rounded-md border"><Checkbox id={`holiday-${h.id}`} checked={selectedHolidays.includes(h.id)} onCheckedChange={c => {
                          if (c) setSelectedHolidays([...selectedHolidays, h.id]);
                          else setSelectedHolidays(selectedHolidays.filter(hid => hid !== h.id));
                        }} /><Label htmlFor={`holiday-${h.id}`}>{h.name}</Label></div>)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

              </div>

              {/* Right Column */}
              <div className="space-y-8">
                {/* Actions and Status */}
                <Card>
                  <CardHeader>
                    <CardTitle>Finalize Edits</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="status">Contract Status</Label>
                      <Select value={formData.status} onValueChange={v => setFormData(p => ({ ...p, status: v as any }))}>
                        <SelectTrigger><SelectValue /></SelectTrigger>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="terminated">Terminated</SelectItem>
                          <SelectItem value="expired">Expired</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="p-4 border rounded-lg bg-gray-50 text-center">
                      <p className="text-sm text-gray-600">Total Salary</p>
                      <p className="text-3xl font-bold text-gray-800">₹{calculateTotalSalary().toLocaleString()}</p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex gap-2">
                    <Button type="button" variant="outline" onClick={() => navigate('/contracts')} className="w-full"><X className="h-4 w-4 mr-2" />Cancel</Button>
                    <Button type="submit" disabled={loading} className="w-full">
                      {loading ? 'Saving...' : <><Save className="h-4 w-4 mr-2" /> Save Changes</>}
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  )
}
