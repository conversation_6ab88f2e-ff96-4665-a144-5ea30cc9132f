
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { OnboardingGuard } from "@/components/auth/OnboardingGuard"
import { OnboardingRoute } from "@/components/auth/OnboardingRoute";
// import Index from "./pages/Dashboard";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import NotFound from "./pages/NotFound";
import Departments from "./pages/masters/Departments";
import Shifts from "./pages/masters/Shifts";
import Holidays from "./pages/masters/Holidays";
import LeaveTypes from "./pages/masters/LeaveTypes";
import Roles from "./pages/masters/Roles";
import Permissions from "./pages/masters/Permissions";
import WorkWeeks from "./pages/masters/WorkWeeks";
import ContractTypes from "./pages/masters/ContractTypes";
import AddContractType from "./pages/masters/AddContractType";
import EditContractType from "./pages/masters/EditContractType";
import SalaryComponents from "./pages/masters/SalaryComponents";

import CompanySettings from "./pages/settings/CompanySettings";
import SmtpSettings from "./pages/settings/SmtpSettings";
import Users from "./pages/Users";
import AddUser from "./pages/AddUser";
import EditUser from "./pages/EditUser";
import Employees from "./pages/Employees";
import AddEmployee from "./pages/AddEmployee";
import EditEmployee from "./pages/EditEmployee";
import Contracts from "./pages/Contracts";
import AddContractGroup from "./pages/AddContractGroup";
import AddContract from "./pages/AddContract";
import ReviseContract from "./pages/ReviseContract";
import EditContract from "./pages/EditContract";
import ContractHistory from "./pages/ContractHistory";
import ContractAcceptance from "./pages/onboarding/ContractAcceptance";
import DocumentUpload from "./pages/onboarding/DocumentUpload";
import WaitingApproval from "./pages/onboarding/WaitingApproval";
import HRApproval from "./pages/HRApproval";
import EmployeeDashboard from "./pages/EmployeeDashboard";
import HRDashboard from "./pages/HRDashboard";
import MyProfile from "./pages/MyProfile";
import MyLeaves from "./pages/MyLeaves";
import Leaves from "./pages/Leaves";
import Payroll from "./pages/Payroll";
import PayrollPeriods from "./pages/PayrollPeriods";
import Attendance from "./pages/Attendance";
import UserPermissions from "./pages/masters/UserPermissions"; // 👈 Import the new page
import Dashboard from "./pages/Dashboard"; 
import { AdminRoute, HRRoute, EmployeeRoute } from "./components/auth/ProtectedRoute";


const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <OnboardingGuard>
            <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route 
              path="/" 
              element={
                <ProtectedRoute>
                  <Dashboard  />
                </ProtectedRoute>
              } 
            />
            {/* <Route 
              path="/employee-dashboard" 
              element={
                <EmployeeRoute>
                  <EmployeeDashboard />
                </EmployeeRoute>
              } 
            /> */}
            {/* <Route 
              path="/hr-dashboard" 
              element={
                <HRRoute>
                  <HRDashboard />
                </HRRoute>
              } 
            /> */}
            <Route 
              path="/my-profile" 
              element={
                <EmployeeRoute>
                  <MyProfile />
                </EmployeeRoute>
              } 
            />
            <Route 
              path="/my-leaves" 
              element={
                <EmployeeRoute>
                  <MyLeaves />
                </EmployeeRoute>
              } 
            />
            <Route 
              path="/leaves" 
              element={
                <ProtectedRoute resource="leaves">
                  <Leaves />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/payroll" 
              element={
                <ProtectedRoute resource="payroll">
                  <Payroll />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/payroll/periods" 
              element={
                <ProtectedRoute resource="payroll">
                  <PayrollPeriods />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/attendance" 
              element={
                <ProtectedRoute resource="attendance">
                  <Attendance />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/departments" 
              element={
                <ProtectedRoute resource="masters_departments">
                  <Departments />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/shifts" 
              element={
                <ProtectedRoute resource="masters_shifts">
                  <Shifts />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/holidays" 
              element={
                <ProtectedRoute resource="masters_holidays">
                  <Holidays />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/leave-types" 
              element={
                <ProtectedRoute resource="masters_leave_types">
                  <LeaveTypes />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/roles" 
              element={
                <ProtectedRoute resource="roles_management">
                  <Roles />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/permissions" 
              element={
                <ProtectedRoute resource="permissions_management">
                  <Permissions />
                </ProtectedRoute>
              } 
            />
             <Route 
      path="/masters/user-permissions" 
      element={
        <ProtectedRoute resource="user_permissions_management">
          <UserPermissions />
        </ProtectedRoute>
      } 
    />
            <Route 
              path="/masters/work-weeks" 
              element={
                <ProtectedRoute resource="masters_work_weeks">
                  <WorkWeeks />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/contract-types" 
              element={
                <ProtectedRoute resource="masters_contract_types">
                  <ContractTypes />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/contract-types/add" 
              element={
                <ProtectedRoute resource="masters_contract_types" action="add">
                  <AddContractType />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/contract-types/edit/:id" 
              element={
                <ProtectedRoute resource="masters_contract_types" action="edit">
                  <EditContractType />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/masters/salary-components" 
              element={
                <ProtectedRoute resource="masters_salary_components">
                  <SalaryComponents />
                </ProtectedRoute>
              } 
            />

            <Route 
              path="/settings/company" 
              element={
                <ProtectedRoute resource="settings_company">
                  <CompanySettings />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/settings/smtp" 
              element={
                <ProtectedRoute  resource="settings_smtp">
                  <SmtpSettings />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/users" 
              element={
                <ProtectedRoute resource="users">
                  <Users />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/users/add" 
              element={
                <ProtectedRoute>
                  <AddUser />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/users/edit/:id" 
              element={
                <ProtectedRoute>
                  <EditUser />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/employees" 
              element={
                <ProtectedRoute resource="employees">
                  <Employees />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/employees/add" 
              element={
                <ProtectedRoute>
                  <AddEmployee />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/employees/edit/:id" 
              element={
                <ProtectedRoute>
                  <EditEmployee />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/contracts" 
              element={
                <ProtectedRoute resource="contracts">
                  <Contracts />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/contracts/groups/add" 
              element={
                <ProtectedRoute>
                  <AddContractGroup />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/contracts/add" 
              element={
                <ProtectedRoute>
                  <AddContract />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/contracts/edit/:id" 
              element={
                <ProtectedRoute resource="contracts" action="edit">
                  <EditContract />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/contracts/revise/:id" 
              element={
                <ProtectedRoute resource="contracts" action="edit">
                  <ReviseContract />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/contracts/history/:id" 
              element={
                <ProtectedRoute resource="contracts">
                  <ContractHistory />
                </ProtectedRoute>
              } 
            />
            <Route
              path="/onboarding/contract"
              element={
                <OnboardingRoute requiredStatus={['pending']}>
                  <ContractAcceptance />
                </OnboardingRoute>
              }
            />
            <Route
              path="/onboarding/documents"
              element={
                <OnboardingRoute requiredStatus={['contract_accepted']}>
                  <DocumentUpload />
                </OnboardingRoute>
              }
            />
            <Route
              path="/onboarding/waiting"
              element={
                <OnboardingRoute requiredStatus={['docs_uploaded']}>
                  <WaitingApproval />
                </OnboardingRoute>
              }
            />
            <Route 
              path="/hr/approvals" 
              element={
                <ProtectedRoute resource="hr">
                  <HRApproval />
                </ProtectedRoute>
              } 
            />
            <Route path="*" element={<NotFound />} />
          </Routes>
          </OnboardingGuard>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
