import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { ArrowLeft, Calendar, DollarSign, FileText, Eye, Edit, Clock, User, Building } from "lucide-react"
import { Layout } from "@/components/Layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { format } from "date-fns"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { usePermissions } from "@/hooks/usePermissions";
interface ContractHistoryItem {
  id: string
  employee_id: string
  // contract_group_id: string
  start_date: string
  end_date: string
  basic_salary: number
  status: string
  // revised_from: string
  // revision_number: number
  version_type: string
  created_at: string
  contract_types: {
    name: string
    code: string
  }

}

export default function ContractHistory() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const { id } = useParams()

  const [loading, setLoading] = useState(true)
  const [contractHistory, setContractHistory] = useState<ContractHistoryItem[]>([])
  const [employeeInfo, setEmployeeInfo] = useState<any>(null)
   const { canAccess, loading: permissionsLoading } = usePermissions();

  useEffect(() => {
    if (id) {
      fetchContractHistory()
    }
  }, [id])

  const fetchContractHistory = async () => {
    try {
      // First get the original contract to find the employee
      const { data: originalContract } = await supabase
        .from('contracts')
        .select('employee_id, contract_group_id')
        .eq('id', id)
        .single()

      if (!originalContract) {
        toast({
          title: "Error",
          description: "Contract not found",
          variant: "destructive",
        })
        return
      }

      // Get employee info
      const { data: employee } = await supabase
        .from('employees')
        .select(`
          employee_code,
          user_profiles(first_name, last_name)
        `)
        .eq('id', originalContract.employee_id)
        .single()

      setEmployeeInfo(employee)

      // Get all contracts for this employee (including revisions)
      const { data: history, error } = await supabase
        .from('contracts')
        .select(`
          id,
          employee_id,
          contract_group_id,
          start_date,
          end_date,
          basic_salary,
          status,
          created_at,
          contract_types(name, code)
        `)
        .eq('employee_id', originalContract.employee_id)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Build revision chains
      const processedHistory = history?.map((contract, index) => ({
        ...contract,
        version_type: index === 0 ? 'Latest' : `Version ${history.length - index}`
      })) || []

      setContractHistory(processedHistory)
    } catch (error) {
      console.error('Error fetching contract history:', error)
      toast({
        title: "Error",
        description: "Failed to fetch contract history",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleActivateContract = async (contractId: string) => {
    try {
      // Deactivate all other contracts for this employee
      await supabase
        .from('contracts')
        .update({ status: 'terminated' })
        .eq('employee_id', contractHistory[0]?.employee_id)
        .eq('status', 'active')

      // Activate the selected contract
      await supabase
        .from('contracts')
        .update({ status: 'active' })
        .eq('id', contractId)

      toast({
        title: "Success",
        description: "Contract activated successfully",
      })

      fetchContractHistory()
    } catch (error) {
      console.error('Error activating contract:', error)
      toast({
        title: "Error",
        description: "Failed to activate contract",
        variant: "destructive",
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-gray-100 text-gray-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'terminated': return 'bg-gray-100 text-gray-800'
      case 'expired': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading contract history...</div>
        </div>
      </Layout>
    )
  }
if (permissionsLoading) { return <Layout><div>Loading...</div></Layout>; }

  // 👇 ADD THIS PAGE GUARD 👇
  if (!canAccess('contracts')) {
    return (
      <Layout>
        <div className="p-8 text-center text-red-600">
          <h2>Access Denied</h2>
          <p>You do not have permission to view contract history.</p>
        </div>
      </Layout>
    );
  }
  return (
    <Layout>
      <div className="min-h-screen bg-white">
        {/* Header */}
        <div className="border-b border-gray-200 sticky top-0 z-40 bg-white">
          <div className="px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/contracts')}
                className="p-2"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">
                  Contract History
                </h1>
                <p className="text-gray-600">
                  {employeeInfo && `${employeeInfo.user_profiles.first_name} ${employeeInfo.user_profiles.last_name} (${employeeInfo.employee_code})`}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="px-4 sm:px-6 lg:px-8 py-6">
          <div className=" mx-auto">
            <Card>
              <CardHeader className="border-b border-gray-200">
                <CardTitle className="text-lg font-medium text-gray-900">
                  All Contract Versions
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50">
                        <TableHead className="text-gray-600">Version</TableHead>
                        <TableHead className="text-gray-600">Contract Type</TableHead>
                        <TableHead className="text-gray-600">Period</TableHead>
                        <TableHead className="text-gray-600">Salary</TableHead>
                        <TableHead className="text-gray-600">Status</TableHead>
                        <TableHead className="text-gray-600">Created</TableHead>
                        <TableHead className="text-gray-600">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {contractHistory.map((contract) => (
                        <TableRow key={contract.id} className="hover:bg-gray-50">
                          <TableCell>
                            <span className="text-gray-900">{contract.version_type}</span>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="text-gray-900">{contract.contract_types?.name}</div>
                              <div className="text-sm text-gray-500">{contract.contract_types?.code}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="text-gray-900">
                                {format(new Date(contract.start_date), "MMM dd, yyyy")}
                              </div>
                              {contract.end_date && (
                                <div className="text-sm text-gray-500">
                                  to {format(new Date(contract.end_date), "MMM dd, yyyy")}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="text-gray-900">₹{contract.basic_salary?.toLocaleString()}</span>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(contract.status)}>
                              {contract.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm text-gray-500">
                              {format(new Date(contract.created_at), "MMM dd, yyyy")}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => navigate(`/contracts/view/${contract.id}`)}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                View
                              </Button>

                              {contract.status === 'draft' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleActivateContract(contract.id)}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  Activate
                                </Button>
                              )}

                              {(contract.status === 'active' || contract.status === 'draft') && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => navigate(`/contracts/revise/${contract.id}`)}
                                >
                                  <Edit className="h-3 w-3 mr-1" />
                                  Revise
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {contractHistory.length === 0 && (
                  <div className="text-center py-12">
                    <FileText className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                    <p className="text-gray-500">No contract history found</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  )
}