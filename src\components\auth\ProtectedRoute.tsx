import { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { usePermissions } from '@/hooks/usePermissions'

interface ProtectedRouteProps {
  children: ReactNode
  resource?: string
  action?: string
  fallback?: ReactNode
}

export const ProtectedRoute = ({ 
  children, 
  resource, 
  action = 'view',
  fallback 
}: ProtectedRouteProps) => {
  const { user, loading } = useAuth()
  const { hasPermission, userRole } = usePermissions()
  const location = useLocation()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // If resource is specified, check permissions (but wait for permissions to load)
  const { loading: permissionsLoading } = usePermissions()
  
  if (permissionsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  if (resource && !hasPermission(resource, action)) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    // Redirect based on role
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}

// Role-specific route protection
export const AdminRoute = ({ children }: { children: ReactNode }) => (
  <ProtectedRoute resource="admin" action="view">
    {children}
  </ProtectedRoute>
)

export const HRRoute = ({ children }: { children: ReactNode }) => (
  <ProtectedRoute resource="hr" action="view">
    {children}
  </ProtectedRoute>
)

export const EmployeeRoute = ({ children }: { children: ReactNode }) => (
  <ProtectedRoute resource="employee" action="view">
    {children}
  </ProtectedRoute>
)