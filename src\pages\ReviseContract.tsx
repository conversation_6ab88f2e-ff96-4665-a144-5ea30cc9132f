import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { ArrowLeft, Calendar, User, FileText, DollarSign, Building, Clock, AlertCircle, Save, X, Settings, Users, Briefcase, CreditCard, Edit, Info } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { format, differenceInDays, addDays } from "date-fns"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

// Interface definitions
interface Contract {
  id: string
  employee_id: string
  contract_group_id: string
  contract_type_id: string
  contract_template_id?: string
  start_date: string
  end_date: string
  status: string
  basic_salary: number
  overtime_allowed: boolean
  overtime_rate: number
  probation_period: number
  notice_period: number
}

interface ContractConflict {
  type: 'gap' | 'overlap'
  message: string
  affectedContract?: Contract
  gapDays?: number
  overlapDays?: number
}

// Main Component
export default function ReviseContract() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const { id } = useParams<{ id: string }>()

  // State management
  const [loading, setLoading] = useState(true)
  const [originalContract, setOriginalContract] = useState<Contract | null>(null)
  const [groupContracts, setGroupContracts] = useState<Contract[]>([])
  const [conflicts, setConflicts] = useState<ContractConflict[]>([])
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [salaryComponents, setSalaryComponents] = useState<any[]>([])
  const [selectedSalaryComponents, setSelectedSalaryComponents] = useState<any[]>([])

  const [formData, setFormData] = useState({
    overtime_allowed: false,
    overtime_rate: "",
    probation_period: "",
    notice_period: "",
  })

  // Data fetching and conflict detection effects
  useEffect(() => {
    if (id) {
      fetchContractData()
    }
  }, [id])

  useEffect(() => {
    if (startDate || endDate) {
      detectConflicts()
    }
  }, [startDate, endDate, groupContracts])

  // Data fetching logic
  const fetchContractData = async () => {
    setLoading(true)
    try {
      const { data: contract, error } = await supabase.from('contracts').select('*').eq('id', id).single()
      if (error) throw error
      setOriginalContract(contract)
      setStartDate(new Date(contract.start_date))
      setEndDate(contract.end_date ? new Date(contract.end_date) : undefined)
      setFormData({
        overtime_allowed: contract.overtime_allowed,
        overtime_rate: contract.overtime_rate?.toString() || "",
        probation_period: contract.probation_period?.toString() || "",
        notice_period: contract.notice_period?.toString() || "",
      })

      const { data: salaryData } = await supabase.from('employee_salary_components').select(`salary_component_id, value, salary_components(*)`).eq('contract_id', id).eq('is_deleted', false)
      const mappedSalary = salaryData?.map(esc => ({ salary_component_id: esc.salary_component_id, value: esc.value, component: esc.salary_components })).filter(sc => sc.component) || []
      setSelectedSalaryComponents(mappedSalary)

      const { data: allComponents } = await supabase.from('salary_components').select('*').eq('is_active', true).eq('is_deleted', false)
      setSalaryComponents(allComponents || [])

      if (contract.contract_group_id) {
        const { data: groupData } = await supabase.from('contracts').select('*').eq('contract_group_id', contract.contract_group_id).neq('id', id).eq('is_active', true).eq('is_deleted', false).order('start_date')
        setGroupContracts(groupData || [])
      }
    } catch (error) {
      console.error('Error fetching contract:', error)
      toast({ title: "Error", description: "Failed to fetch contract data", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  // Conflict detection logic
  const detectConflicts = () => {
    if (!startDate || !originalContract) return
    const newConflicts: ContractConflict[] = []
    groupContracts.forEach(contract => {
      const contractStart = new Date(contract.start_date)
      const contractEnd = contract.end_date ? new Date(contract.end_date) : null
      if (endDate && contractEnd && startDate <= contractEnd && endDate >= contractStart) {
        newConflicts.push({ type: 'overlap', message: `Overlaps with contract from ${format(contractStart, "PPP")} to ${format(contractEnd, "PPP")}`, affectedContract: contract })
      }
    })
    // Simple gap detection (informational)
    const nextContract = groupContracts.find(c => new Date(c.start_date) > (endDate || new Date()));
    if (endDate && nextContract) {
      const gap = differenceInDays(new Date(nextContract.start_date), endDate) - 1;
      if (gap > 0) {
        newConflicts.push({ type: 'gap', message: `Creates a ${gap}-day gap before the next contract.` });
      }
    }
    setConflicts(newConflicts)
  }

  // Salary calculation
  const calculateTotalSalary = () => {
    const earnings = selectedSalaryComponents.filter(sc => sc.component?.component_type === 'earning').reduce((sum, sc) => sum + Number(sc.value), 0)
    const deductions = selectedSalaryComponents.filter(sc => sc.component?.component_type === 'deduction').reduce((sum, sc) => sum + Number(sc.value), 0)
    return earnings - deductions
  }

  // Validation logic
  const validateRevision = () => {
    const errors: string[] = []
    if (!startDate) errors.push("Start date is required")
    if (selectedSalaryComponents.length === 0) errors.push("At least one salary component is required")
    if (conflicts.some(c => c.type === 'overlap')) errors.push("Contract overlaps must be resolved")
    setValidationErrors(errors)
    return errors.length === 0
  }

  // Navigation handler
  const handleEditConflictingContract = (contractId: string) => {
    navigate(`/contracts/revise/${contractId}`)
  }

  // Form submission handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateRevision() || !originalContract) {
      toast({ title: "Validation Error", description: "Please fix errors before submitting.", variant: "destructive" })
      return
    }
    setLoading(true)
    try {
      const totalSalary = calculateTotalSalary()
      const revisedContractData = {
        employee_id: originalContract.employee_id,
        contract_group_id: originalContract.contract_group_id,
        contract_type_id: originalContract.contract_type_id,
        contract_template_id: originalContract.contract_template_id,
        start_date: startDate?.toISOString().split('T')[0],
        end_date: endDate ? endDate.toISOString().split('T')[0] : null,
        basic_salary: totalSalary,
        overtime_allowed: formData.overtime_allowed,
        overtime_rate: formData.overtime_rate ? parseFloat(formData.overtime_rate) : null,
        probation_period: formData.probation_period ? parseInt(formData.probation_period) : null,
        notice_period: formData.notice_period ? parseInt(formData.notice_period) : null,
        status: (originalContract.status === 'active' ? 'active' : 'draft') as "active" | "expired" | "terminated" | "draft"
      }

      // Insert new revised contract
      const { data: newContract, error: insertError } = await supabase.from('contracts').insert(revisedContractData).select().single()
      if (insertError) throw insertError

      // Update old contract to terminated/revised status
      await supabase.from('contracts').update({ status: 'terminated' }).eq('id', originalContract.id)

      // Update salary components
      await supabase.from('employee_salary_components').update({ is_active: false, is_deleted: true }).eq('contract_id', originalContract.id)
      const salaryInserts = selectedSalaryComponents.map(sc => ({
        employee_id: originalContract.employee_id,
        contract_id: newContract.id,
        salary_component_id: sc.salary_component_id,
        value: sc.value,
        effective_from: newContract.start_date,
        effective_to: newContract.end_date,
        is_active: true,
        is_deleted: false,
      }))
      await supabase.from('employee_salary_components').insert(salaryInserts)

      toast({ title: "Success", description: "Contract revised successfully." })
      navigate('/contracts')
    } catch (error) {
      console.error('Error revising contract:', error)
      toast({ title: "Error", description: "Failed to revise contract.", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  if (loading && !originalContract) {
    return <Layout><div className="flex justify-center items-center h-screen">Loading...</div></Layout>
  }

  if (!originalContract) {
    return <Layout><div className="flex justify-center items-center h-screen">Contract not found.</div></Layout>
  }

  return (
    <Layout>
      <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 min-h-screen">
        <div className=" mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button variant="ghost" size="icon" onClick={() => navigate('/contracts')}><ArrowLeft className="h-5 w-5" /></Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Revise Contract</h1>
              <p className="text-sm text-gray-600">Create a new version of the contract with updated terms.</p>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column */}
              <div className="lg:col-span-2 space-y-8">
                {/* Timeline & Policies */}
                <Card>
                  <CardHeader>
                    <CardTitle>Revised Timeline & Policies</CardTitle>
                    <CardDescription>Adjust the contract dates and policy terms.</CardDescription>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label>Start Date *</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className={cn("w-full justify-start text-left font-normal", !startDate && "text-muted-foreground")}>
                            <Calendar className="mr-2 h-4 w-4" />
                            {startDate ? format(startDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0"><CalendarComponent mode="single" selected={startDate} onSelect={setStartDate} initialFocus /></PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label>End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className={cn("w-full justify-start text-left font-normal", !endDate && "text-muted-foreground")}>
                            <Calendar className="mr-2 h-4 w-4" />
                            {endDate ? format(endDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0"><CalendarComponent mode="single" selected={endDate} onSelect={setEndDate} initialFocus /></PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="probation_period">Probation Period (months)</Label>
                      <Input id="probation_period" type="number" value={formData.probation_period} onChange={(e) => setFormData({ ...formData, probation_period: e.target.value })} placeholder="e.g., 3" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="notice_period">Notice Period (days)</Label>
                      <Input id="notice_period" type="number" value={formData.notice_period} onChange={(e) => setFormData({ ...formData, notice_period: e.target.value })} placeholder="e.g., 30" />
                    </div>
                  </CardContent>
                </Card>

                {/* Financial Details */}
                <Card>
                  <CardHeader>
                    <CardTitle>Revised Financial Details</CardTitle>
                    <CardDescription>Adjust salary components and overtime settings.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <Label className="font-semibold">Salary Components *</Label>
                      <div className="max-h-80 overflow-y-auto space-y-3 border rounded-lg p-4 bg-gray-50">
                        {salaryComponents.map((component) => {
                          const selectedComponent = selectedSalaryComponents.find(sc => sc.salary_component_id === component.id);
                          return (
                            <div key={component.id} className="p-3 bg-white rounded-md border">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <Checkbox
                                    id={`comp-${component.id}`}
                                    checked={!!selectedComponent}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setSelectedSalaryComponents([...selectedSalaryComponents, { salary_component_id: component.id, value: component.default_value || 0, component }]);
                                      } else {
                                        setSelectedSalaryComponents(selectedSalaryComponents.filter(sc => sc.salary_component_id !== component.id));
                                      }
                                    }}
                                  />
                                  <div>
                                    <Label htmlFor={`comp-${component.id}`} className="font-medium text-gray-800">{component.name}</Label>
                                    <p className="text-xs text-gray-500">{component.code} - {component.component_type}</p>
                                  </div>
                                </div>
                                {selectedComponent && (
                                  <div className="w-40">
                                    <Input type="number" step="0.01" min="0" value={selectedComponent.value}
                                      onChange={(e) => {
                                        const updated = selectedSalaryComponents.map(sc => sc.salary_component_id === component.id ? { ...sc, value: parseFloat(e.target.value) || 0 } : sc);
                                        setSelectedSalaryComponents(updated);
                                      }}
                                      className="h-8 text-right" placeholder="Amount"
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="overtime_allowed" checked={formData.overtime_allowed} onCheckedChange={(checked) => setFormData({ ...formData, overtime_allowed: checked as boolean })} />
                        <Label htmlFor="overtime_allowed">Allow Overtime Work</Label>
                      </div>
                      {formData.overtime_allowed && (
                        <div className="space-y-2 pl-6">
                          <Label htmlFor="overtime_rate">Overtime Rate (per hour)</Label>
                          <Input id="overtime_rate" type="number" value={formData.overtime_rate} onChange={(e) => setFormData({ ...formData, overtime_rate: e.target.value })} placeholder="Enter rate" />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column */}
              <div className="space-y-8">
                {/* Actions and Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Finalize Revision</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="p-4 border rounded-lg bg-gray-50 text-center">
                      <p className="text-sm text-gray-600">New Total Salary</p>
                      <p className="text-3xl font-bold text-gray-800">₹{calculateTotalSalary().toLocaleString()}</p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex gap-2">
                    <Button type="button" variant="outline" onClick={() => navigate('/contracts')} className="w-full"><X className="h-4 w-4 mr-2" />Cancel</Button>
                    <Button type="submit" disabled={loading || conflicts.some(c => c.type === 'overlap')} className="w-full">
                      {loading ? 'Revising...' : <><Save className="h-4 w-4 mr-2" /> Revise Contract</>}
                    </Button>
                  </CardFooter>
                </Card>

                {/* Conflicts & Validation */}
                {(conflicts.length > 0 || validationErrors.length > 0) && (
                  <Card className="border-yellow-200 bg-yellow-50">
                    <CardHeader>
                      <CardTitle className="text-base text-yellow-800 flex items-center gap-2"><AlertCircle className="h-5 w-5" /> Review Required</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {validationErrors.map((error, index) => (
                        <Alert key={`val-${index}`} variant="destructive"><AlertCircle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>
                      ))}
                      {conflicts.map((conflict, index) => (
                        <Alert key={`con-${index}`} variant={conflict.type === 'overlap' ? 'destructive' : 'default'} className={conflict.type === 'gap' ? 'bg-yellow-100 border-yellow-300' : ''}>
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription className="flex justify-between items-center">
                            {conflict.message}
                            {conflict.affectedContract && (
                              <Button type="button" size="sm" variant="outline" onClick={() => handleEditConflictingContract(conflict.affectedContract!.id)}><Edit className="h-3 w-3 mr-1" /> Edit</Button>
                            )}
                          </AlertDescription>
                        </Alert>
                      ))}
                    </CardContent>
                  </Card>
                )}

                {/* Original Contract Info */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2"><Info className="h-4 w-4 text-blue-600" /> Original Contract Details</CardTitle>
                  </CardHeader>
                  <CardContent className="text-sm text-gray-600 space-y-1">
                    <p><strong>Period:</strong> {format(new Date(originalContract.start_date), "PPP")} - {originalContract.end_date ? format(new Date(originalContract.end_date), "PPP") : "Ongoing"}</p>
                    <p><strong>Status:</strong> <span className="font-semibold capitalize">{originalContract.status}</span></p>
                    <p><strong>Original Salary:</strong> ₹{originalContract.basic_salary?.toLocaleString()}</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  )
}
