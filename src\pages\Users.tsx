import { useState, useEffect } from "react"
import { <PERSON>, useNavigate } from "react-router-dom"
import { Plus, Search, Edit, Trash2, Eye, MoreHorizontal, ChevronLeft, ChevronRight, Users as UsersIcon, Filter, Download } from "lucide-react"
import { Layout } from "@/components/Layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { usePermissions } from "@/hooks/usePermissions"


interface User {
  id: string
  first_name: string
  last_name: string 
  personal_email: string
  phone: string
  is_active: boolean
  created_at: string
  user_roles: {
    roles: {
      name: string
    }
  }[]
  employees: {
    employee_code: string
    company_email: string
  } | null
}

export default function Users() {
  const [users, setUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const { toast } = useToast()
  const navigate = useNavigate()
  const { hasPermission, canAccess, loading: permissionsLoading } = usePermissions()
  
  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select(`
          id,
          first_name,
          last_name,
          personal_email,
          phone,
          is_active,
          created_at
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Fetch user roles and employees separately to avoid complex joins
      const userIds = data?.map(user => user.id) || []

      const { data: userRoles, error: rolesError } = await supabase
        .from('user_roles')
        .select(`
          user_id,
          roles (
            name
          )
        `)
        .in('user_id', userIds)
        .eq('is_active', true)

      const { data: employees, error: employeesError } = await supabase
        .from('employees')
        .select(`
          user_id,
          employee_code,
          company_email
        `)
        .in('user_id', userIds)
        .eq('is_active', true)

      if (rolesError) throw rolesError
      if (employeesError) throw employeesError

      // Combine the data
      const combinedData = data?.map(user => ({
        ...user,
        user_roles: userRoles?.filter(role => role.user_id === user.id) || [],
        employees: employees?.find(emp => emp.user_id === user.id) || null
      })) || []

      setUsers(combinedData)
    } catch (error) {
      console.error('Error fetching users:', error)
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredUsers = users.filter(user =>
    user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.personal_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.employees?.employee_code?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Pagination calculations
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage)

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1)
  }, [searchTerm])

  const handleView = (user: User) => {
    setSelectedUser(user)
    setViewDialogOpen(true)
  }

  const handleEdit = (userId: string) => {
    navigate(`/users/edit/${userId}`)
  }

  const handleDelete = async (userId: string) => {
    if (confirm('Are you sure you want to delete this user?')) {
      try {
        const { error } = await supabase
          .from('user_profiles')
          .update({ is_deleted: true })
          .eq('id', userId)

        if (error) throw error

        toast({
          title: "Success",
          description: "User deleted successfully",
        })
        fetchUsers()
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete user",
          variant: "destructive",
        })
      }
    }
  }

  if (loading || permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!canAccess('users')) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-red-600">Access Denied: You don't have permission to view users.</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50/50 dark:bg-gray-900/50">
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          {/* Header Section */}
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
              <div className="space-y-1">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <UsersIcon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Users</h1>
                    <p className="text-gray-600 dark:text-gray-400">Manage user accounts and permissions</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="bg-white dark:bg-gray-800 rounded-lg px-4 py-2 border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex items-center gap-2 text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Total Users:</span>
                    <span className="font-semibold text-gray-900 dark:text-white">{users.length}</span>
                  </div>
                </div>
                {hasPermission('users', 'add') && (
                  <Link to="/users/add">
                    <Button className="bg-primary hover:bg-primary/90 text-white shadow-sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add User
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <Card className="mb-6 border-0 shadow-sm bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by name, email, or employee code..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 dark:border-gray-700 focus:border-primary focus:ring-primary"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" className="border-gray-200 dark:border-gray-700">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" size="sm" className="border-gray-200 dark:border-gray-700">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Users Table/Cards */}
          <Card className="border-0 shadow-sm bg-white dark:bg-gray-800 overflow-hidden">
            <CardContent className="p-0">
              {/* Mobile Card View */}
              <div className="block lg:hidden">
                <div className="divide-y divide-gray-100 dark:divide-gray-700">
                  {paginatedUsers.map((user, index) => (
                    <div key={user.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                              <span className="text-sm font-semibold text-primary">
                                {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {user.first_name} {user.last_name}
                              </h3>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                #{startIndex + index + 1}
                              </p>
                            </div>
                          </div>

                          <div className="space-y-2 mb-4">
                            <div className="flex items-center gap-2 text-sm">
                              <span className="text-gray-500 dark:text-gray-400">Personal:</span>
                              <span className="text-gray-900 dark:text-white">{user.personal_email}</span>
                            </div>
                            {user.employees?.company_email && (
                              <div className="flex items-center gap-2 text-sm">
                                <span className="text-gray-500 dark:text-gray-400">Company:</span>
                                <span className="text-gray-900 dark:text-white">{user.employees.company_email}</span>
                              </div>
                            )}
                            {user.phone && (
                              <div className="flex items-center gap-2 text-sm">
                                <span className="text-gray-500 dark:text-gray-400">Phone:</span>
                                <span className="text-gray-900 dark:text-white">{user.phone}</span>
                              </div>
                            )}
                            {user.employees?.employee_code && (
                              <div className="flex items-center gap-2 text-sm">
                                <span className="text-gray-500 dark:text-gray-400">Code:</span>
                                <span className="text-gray-900 dark:text-white">{user.employees.employee_code}</span>
                              </div>
                            )}
                          </div>

                          <div className="flex flex-wrap gap-2 mb-4">
                            {user.user_roles.map((userRole, roleIndex) => (
                              <Badge key={roleIndex} variant="secondary" className="text-xs">
                                {userRole.roles.name}
                              </Badge>
                            ))}
                            <Badge
                              variant={user.is_active ? "default" : "secondary"}
                              className={user.is_active ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" : ""}
                            >
                              {user.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                        </div>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuItem onClick={() => handleView(user)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            {hasPermission('users', 'edit') && (
                              <DropdownMenuItem onClick={() => handleEdit(user.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit User
                              </DropdownMenuItem>
                            )}
                            {hasPermission('users', 'delete') && (
                              <DropdownMenuItem
                                onClick={() => handleDelete(user.id)}
                                className="text-red-600 focus:text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete User
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))}

                  {paginatedUsers.length === 0 && (
                    <div className="text-center py-12">
                      <UsersIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No users found</h3>
                      <p className="text-gray-500 dark:text-gray-400">Try adjusting your search criteria</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Desktop Table View */}
              <div className="hidden lg:block">
                <div className="overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700">
                        <TableHead className="font-semibold text-gray-900 dark:text-white w-16">#</TableHead>
                        <TableHead className="font-semibold text-gray-900 dark:text-white">User</TableHead>
                        <TableHead className="font-semibold text-gray-900 dark:text-white">Contact</TableHead>
                        <TableHead className="font-semibold text-gray-900 dark:text-white">Roles</TableHead>
                        <TableHead className="font-semibold text-gray-900 dark:text-white">Employee Info</TableHead>
                        <TableHead className="font-semibold text-gray-900 dark:text-white">Status</TableHead>
                        <TableHead className="font-semibold text-gray-900 dark:text-white text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paginatedUsers.map((user, index) => (
                        <TableRow
                          key={user.id}
                          className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors border-b border-gray-100 dark:border-gray-800"
                        >
                          <TableCell className="font-medium text-gray-500 dark:text-gray-400 w-16">
                            {startIndex + index + 1}
                          </TableCell>

                          <TableCell>
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                                <span className="text-sm font-semibold text-primary">
                                  {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                                </span>
                              </div>
                              <div>
                                <div className="font-semibold text-gray-900 dark:text-white">
                                  {user.first_name} {user.last_name}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  ID: {user.id.slice(0, 8)}...
                                </div>
                              </div>
                            </div>
                          </TableCell>

                          <TableCell>
                            <div className="space-y-1">
                              {user.personal_email && (
                                <div className="text-sm text-gray-900 dark:text-white">
                                  {user.personal_email}
                                </div>
                              )}
                              {user.employees?.company_email && (
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {user.employees.company_email}
                                </div>
                              )}
                              {user.phone && (
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {user.phone}
                                </div>
                              )}
                            </div>
                          </TableCell>

                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {user.user_roles.map((userRole, roleIndex) => (
                                <Badge key={roleIndex} variant="secondary" className="text-xs">
                                  {userRole.roles.name}
                                </Badge>
                              ))}
                              {user.user_roles.length === 0 && (
                                <span className="text-sm text-gray-400 dark:text-gray-500">No roles</span>
                              )}
                            </div>
                          </TableCell>

                          <TableCell>
                            <div className="text-sm">
                              {user.employees?.employee_code ? (
                                <div className="font-mono text-gray-900 dark:text-white">
                                  {user.employees.employee_code}
                                </div>
                              ) : (
                                <span className="text-gray-400 dark:text-gray-500">-</span>
                              )}
                            </div>
                          </TableCell>

                          <TableCell>
                            <Badge
                              variant={user.is_active ? "default" : "secondary"}
                              className={user.is_active
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 border-green-200 dark:border-green-800"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
                              }
                            >
                              {user.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </TableCell>

                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleView(user)}
                                className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/20"
                                title="View Details"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              {hasPermission('users', 'edit') && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEdit(user.id)}
                                  className="h-8 w-8 p-0 hover:bg-amber-50 hover:text-amber-600 dark:hover:bg-amber-900/20"
                                  title="Edit User"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              )}
                              {hasPermission('users', 'delete') && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDelete(user.id)}
                                  className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/20"
                                  title="Delete User"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}

                      {paginatedUsers.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-12">
                            <div className="flex flex-col items-center">
                              <UsersIcon className="h-12 w-12 text-gray-400 mb-4" />
                              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No users found</h3>
                              <p className="text-gray-500 dark:text-gray-400">Try adjusting your search criteria</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>

            {/* Pagination */}
            {filteredUsers.length > 0 && (
              <div className="border-t border-gray-100 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50 px-6 py-4">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                  <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
                    <span>Show</span>
                    <Select
                      value={itemsPerPage.toString()}
                      onValueChange={(value) => { setItemsPerPage(parseInt(value)); setCurrentPage(1); }}
                    >
                      <SelectTrigger className="w-20 h-8 border-gray-200 dark:border-gray-700">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5</SelectItem>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                      </SelectContent>
                    </Select>
                    <span>of {filteredUsers.length} users</span>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </Button>

                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNumber = i + 1;
                        if (totalPages > 5) {
                          if (currentPage <= 3) {
                            pageNumber = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNumber = totalPages - 4 + i;
                          } else {
                            pageNumber = currentPage - 2 + i;
                          }
                        }

                        return (
                          <Button
                            key={pageNumber}
                            variant={currentPage === pageNumber ? "default" : "outline"}
                            size="sm"
                            className={`w-8 h-8 p-0 ${
                              currentPage === pageNumber
                                ? "bg-primary text-white"
                                : "border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
                            }`}
                            onClick={() => setCurrentPage(pageNumber)}
                          >
                            {pageNumber}
                          </Button>
                        );
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>

                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Page {currentPage} of {totalPages}
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* View User Dialog */}
          <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
            <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
              <DialogHeader className="pb-6 border-b border-gray-100 dark:border-gray-700">
                <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-white">
                  User Details
                </DialogTitle>
              </DialogHeader>

              {selectedUser && (
                <div className="space-y-6 pt-6">
                  {/* User Avatar and Basic Info */}
                  <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-xl font-bold text-primary">
                        {selectedUser.first_name.charAt(0)}{selectedUser.last_name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {selectedUser.first_name} {selectedUser.last_name}
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400">
                        User ID: {selectedUser.id.slice(0, 8)}...
                      </p>
                      <div className="mt-2">
                        <Badge
                          variant={selectedUser.is_active ? "default" : "secondary"}
                          className={selectedUser.is_active
                            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                            : ""
                          }
                        >
                          {selectedUser.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                      Contact Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Personal Email</label>
                        <p className="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                          {selectedUser.personal_email || 'Not provided'}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Company Email</label>
                        <p className="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                          {selectedUser.employees?.company_email || 'Not provided'}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Phone Number</label>
                        <p className="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                          {selectedUser.phone || 'Not provided'}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Employee Code</label>
                        <p className="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-800 p-3 rounded-md font-mono">
                          {selectedUser.employees?.employee_code || 'Not assigned'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Roles and Permissions */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                      Roles & Permissions
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedUser.user_roles.length > 0 ? (
                        selectedUser.user_roles.map((userRole, index) => (
                          <Badge key={index} variant="secondary" className="text-sm px-3 py-1">
                            {userRole.roles.name}
                          </Badge>
                        ))
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400 italic">No roles assigned</p>
                      )}
                    </div>
                  </div>

                  {/* Account Information */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                      Account Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Created Date</label>
                        <p className="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                          {new Date(selectedUser.created_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </Layout>
  )
}