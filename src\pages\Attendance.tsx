import { useState, useEffect } from "react"
import { Calendar as CalendarI<PERSON>, Users, CheckCircle, XCircle, Filter, Search } from "lucide-react"
import { Layout } from "@/components/Layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { format, startOfMonth, endOfMonth, eachDayOfInterval } from "date-fns"

interface Employee {
  id: string
  employee_code: string
  user_profiles: {
    first_name: string
    last_name: string
  }
}

interface AttendanceRecord {
  id: string
  employee_id: string
  date: string
  status: 'present' | 'absent' | 'half_day' | 'holiday' | 'leave'
  punch_in_time?: string
  punch_out_time?: string
  total_hours?: number
  employees: Employee
}

export default function Attendance() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [employees, setEmployees] = useState<Employee[]>([])
  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord[]>([])
  const [monthlyAttendance, setMonthlyAttendance] = useState<AttendanceRecord[]>([])
  const [selectedEmployee, setSelectedEmployee] = useState<string>("")
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    fetchEmployees()
    fetchTodayAttendance()
  }, [])

  useEffect(() => {
    if (selectedEmployee) {
      fetchMonthlyAttendance(selectedEmployee, selectedDate)
    }
  }, [selectedEmployee, selectedDate])

  const fetchEmployees = async () => {
    try {
      const { data: employeesData, error } = await supabase
        .from('employees')
        .select(`
          id,
          employee_code,
          user_id
        `)
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('employee_code')

      if (error) throw error

      // Fetch user profiles
      const employeeUserIds = employeesData?.map(emp => emp.user_id) || []
      const { data: profiles } = await supabase
        .from('user_profiles')
        .select('id, first_name, last_name')
        .in('id', employeeUserIds)

      const employeesWithProfiles = employeesData?.map(emp => ({
        ...emp,
        user_profiles: profiles?.find(p => p.id === emp.user_id) || {
          first_name: '',
          last_name: ''
        }
      })) || []

      setEmployees(employeesWithProfiles)
    } catch (error) {
      console.error('Error fetching employees:', error)
      toast({ title: "Error", description: "Failed to fetch employees", variant: "destructive" })
    }
  }

  const fetchTodayAttendance = async () => {
    try {
      const today = format(new Date(), 'yyyy-MM-dd')
      
      // Get all employees and their attendance for today
      const { data: attendanceData, error } = await supabase
        .from('attendance')
        .select(`
          *,
          employees!inner(
            id,
            employee_code,
            user_id
          )
        `)
        .eq('date', today)
        .eq('is_active', true)
        .eq('is_deleted', false)

      if (error) throw error

      // Get user profiles
      const employeeUserIds = attendanceData?.map(att => att.employees.user_id) || []
      const { data: profiles } = await supabase
        .from('user_profiles')
        .select('id, first_name, last_name')
        .in('id', employeeUserIds)

      // Get all employees to show those without attendance records
      const { data: allEmployees } = await supabase
        .from('employees')
        .select(`
          id,
          employee_code,
          user_id
        `)
        .eq('is_active', true)
        .eq('is_deleted', false)

      const allEmployeeUserIds = allEmployees?.map(emp => emp.user_id) || []
      const { data: allProfiles } = await supabase
        .from('user_profiles')
        .select('id, first_name, last_name')
        .in('id', allEmployeeUserIds)

      // Create attendance records for all employees
      const todayRecords = allEmployees?.map(employee => {
        const existingAttendance = attendanceData?.find(att => att.employee_id === employee.id)
        
        return {
          id: existingAttendance?.id || `temp-${employee.id}`,
          employee_id: employee.id,
          date: today,
          status: existingAttendance?.status || 'absent',
          punch_in_time: existingAttendance?.punch_in_time,
          punch_out_time: existingAttendance?.punch_out_time,
          total_hours: existingAttendance?.total_hours,
          employees: {
            ...employee,
            user_profiles: allProfiles?.find(p => p.id === employee.user_id) || {
              first_name: '',
              last_name: ''
            }
          }
        }
      }) || []

      setTodayAttendance(todayRecords)
    } catch (error) {
      console.error('Error fetching today attendance:', error)
      toast({ title: "Error", description: "Failed to fetch today's attendance", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  const fetchMonthlyAttendance = async (employeeId: string, date: Date) => {
    try {
      const startDate = format(startOfMonth(date), 'yyyy-MM-dd')
      const endDate = format(endOfMonth(date), 'yyyy-MM-dd')

      const { data, error } = await supabase
        .from('attendance')
        .select(`
          *,
          employees!inner(
            id,
            employee_code,
            user_id
          )
        `)
        .eq('employee_id', employeeId)
        .gte('date', startDate)
        .lte('date', endDate)
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('date')

      if (error) throw error

      // Get user profile
      const employee = employees.find(emp => emp.id === employeeId)
      const recordsWithProfile = data?.map(record => ({
        ...record,
        employees: {
          ...record.employees,
          user_profiles: employee?.user_profiles || { first_name: '', last_name: '' }
        }
      })) || []

      setMonthlyAttendance(recordsWithProfile)
    } catch (error) {
      console.error('Error fetching monthly attendance:', error)
      toast({ title: "Error", description: "Failed to fetch monthly attendance", variant: "destructive" })
    }
  }

  const getStatusBadge = (status: string) => {
    const styles = {
      present: "bg-green-100 text-green-800",
      absent: "bg-red-100 text-red-800",
      half_day: "bg-yellow-100 text-yellow-800",
      holiday: "bg-blue-100 text-blue-800",
      leave: "bg-purple-100 text-purple-800"
    }
    return <Badge className={styles[status as keyof typeof styles] || styles.absent}>{status.replace('_', ' ')}</Badge>
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const filteredTodayAttendance = todayAttendance.filter(record =>
    record.employees.user_profiles.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.employees.user_profiles.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.employees.employee_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const presentCount = todayAttendance.filter(record => record.status === 'present').length
  const totalEmployees = todayAttendance.length

  const getMonthlyCalendarData = () => {
    const monthStart = startOfMonth(selectedDate)
    const monthEnd = endOfMonth(selectedDate)
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd })
    
    return daysInMonth.map(day => {
      const dayStr = format(day, 'yyyy-MM-dd')
      const attendance = monthlyAttendance.find(att => att.date === dayStr)
      return {
        date: day,
        status: attendance?.status || 'absent',
        hasRecord: !!attendance
      }
    })
  }

  return (
    <Layout>
      <div className="space-y-6">
        <Card>
          <CardHeader className="bg-gray-50 border-b">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Attendance Management
                </CardTitle>
                <p className="text-muted-foreground mt-1 text-sm">Track and manage employee attendance</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Today's Attendance</p>
                <p className="text-2xl font-bold text-green-600">
                  {presentCount}/{totalEmployees}
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs defaultValue="today" className="w-full">
              <TabsList className="border-b w-full justify-start rounded-none px-6">
                <TabsTrigger value="today">Today's Attendance</TabsTrigger>
                <TabsTrigger value="monthly">Monthly View</TabsTrigger>
              </TabsList>

              <TabsContent value="today" className="p-6">
                <div className="flex items-center gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search employees..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="grid gap-4">
                  {filteredTodayAttendance.map(record => (
                    <Card key={record.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          {getStatusIcon(record.status)}
                          <div>
                            <p className="font-medium">
                              {record.employees.user_profiles.first_name} {record.employees.user_profiles.last_name}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {record.employees.employee_code}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          {record.punch_in_time && (
                            <div className="text-sm">
                              <span className="text-muted-foreground">In: </span>
                              {format(new Date(record.punch_in_time), 'HH:mm')}
                            </div>
                          )}
                          {record.punch_out_time && (
                            <div className="text-sm">
                              <span className="text-muted-foreground">Out: </span>
                              {format(new Date(record.punch_out_time), 'HH:mm')}
                            </div>
                          )}
                          {record.total_hours && (
                            <div className="text-sm">
                              <span className="text-muted-foreground">Hours: </span>
                              {record.total_hours}h
                            </div>
                          )}
                          {getStatusBadge(record.status)}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="monthly" className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-1 space-y-4">
                    <div>
                      <label className="text-sm font-medium">Select Employee</label>
                      <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose employee" />
                        </SelectTrigger>
                        <SelectContent>
                          {employees.map(employee => (
                            <SelectItem key={employee.id} value={employee.id}>
                              {employee.user_profiles.first_name} {employee.user_profiles.last_name} ({employee.employee_code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {selectedEmployee && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Monthly Summary</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span>Present Days:</span>
                              <span className="font-medium text-green-600">
                                {monthlyAttendance.filter(att => att.status === 'present').length}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Absent Days:</span>
                              <span className="font-medium text-red-600">
                                {monthlyAttendance.filter(att => att.status === 'absent').length}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Half Days:</span>
                              <span className="font-medium text-yellow-600">
                                {monthlyAttendance.filter(att => att.status === 'half_day').length}
                              </span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>

                  <div className="lg:col-span-2">
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={(date) => date && setSelectedDate(date)}
                      className="rounded-md border"
                      components={{
                        Day: ({ date, ...props }) => {
                          const dayData = getMonthlyCalendarData().find(d => 
                            format(d.date, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
                          )
                          
                          return (
                            <div
                              {...props}
                              className={`
                                relative p-2 text-center cursor-pointer rounded
                                ${dayData?.status === 'present' ? 'bg-green-100 text-green-800' : ''}
                                ${dayData?.status === 'absent' ? 'bg-red-100 text-red-800' : ''}
                                ${dayData?.status === 'half_day' ? 'bg-yellow-100 text-yellow-800' : ''}
                                ${!dayData?.hasRecord ? 'text-gray-400' : ''}
                              `}
                            >
                              {format(date, 'd')}
                            </div>
                          )
                        }
                      }}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </Layout>
  )
}