import { useState, useEffect } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { Upload, FileText, SkipFor<PERSON>, Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/integrations/supabase/client"


export default function DocumentUpload() {
  const navigate = useNavigate()
  const location = useLocation()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [fetchingDocs, setFetchingDocs] = useState(false)
  const [documents, setDocuments] = useState<any[]>([])
  const [completedDocs, setCompletedDocs] = useState<Set<string>>(new Set())
  
  const { employeeId, contractId, requiredDocs } = location.state || {}

  useEffect(() => {
    if (!employeeId || !contractId) {
      navigate('/dashboard')
      return
    }

    console.log('Required docs received:', requiredDocs)

    // If requiredDocs is not provided or empty, try to fetch them
    if (!requiredDocs || !Array.isArray(requiredDocs) || requiredDocs.length === 0) {
      fetchRequiredDocuments()
    } else {
      setDocuments(requiredDocs.map((doc) => ({
        ...doc,
        file: null,
        skipReason: ''
      })))
    }
  }, [employeeId, contractId, requiredDocs, navigate])

  const fetchRequiredDocuments = async () => {
    setFetchingDocs(true)
    try {
      const { data: requiredDocsData, error: docsError } = await supabase
        .from('contracts')
        .select(`
          contract_type_required_documents!inner(
            document_type,
            is_mandatory,
            remarks
          )
        `)
        .eq('id', contractId)
        .eq('contract_type_required_documents.is_active', true)
        .eq('contract_type_required_documents.is_deleted', false)

      if (docsError) {
        console.error('Error fetching required documents:', docsError)
        toast({
          title: "Error",
          description: "Failed to load required documents",
          variant: "destructive"
        })
        setDocuments([])
        return
      }

      // Extract the required documents from the nested structure
      const requiredDocs = requiredDocsData?.[0]?.contract_type_required_documents || []

      console.log('Fetched required documents:', requiredDocs)
      if (Array.isArray(requiredDocs)) {
      setDocuments(requiredDocs.map((doc) => ({
        ...doc,
        file: null,
        skipReason: ''
      })))
    }
    } catch (error) {
      console.error('Error fetching required documents:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load required documents",
        variant: "destructive"
      })
      setDocuments([])
    } finally {
      setFetchingDocs(false)
    }
  }

  const handleFileUpload = async (docIndex: number, file: File) => {
    try {


      const fileExt = file.name.split('.').pop()
      const timestamp = Date.now()
      const fileName = `${employeeId}/${documents[docIndex].document_type}_${timestamp}.${fileExt}`

      console.log('Uploading file:', fileName)

      // First, try to remove any existing file with the same document type
      const existingFiles = await supabase.storage
        .from('employee-documents')
        .list(employeeId.toString(), {
          search: documents[docIndex].document_type
        })

      if (existingFiles.data && existingFiles.data.length > 0) {
        for (const existingFile of existingFiles.data) {
          await supabase.storage
            .from('employee-documents')
            .remove([`${employeeId}/${existingFile.name}`])
        }
      }

      const { error: uploadError } = await supabase.storage
        .from('employee-documents')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        })

      if (uploadError) {
        console.error('Upload error:', uploadError)
        throw uploadError
      }

      const { data } = supabase.storage
        .from('employee-documents')
        .getPublicUrl(fileName)

      console.log('File uploaded successfully, URL:', data.publicUrl)

      await supabase.rpc('handle_employee_document' as any, {
        p_employee_id: employeeId,
        p_contract_id: contractId,
        p_document_type: documents[docIndex].document_type,
        p_file_url: data.publicUrl,
        p_is_skipped: false
      })

      setCompletedDocs(prev => new Set([...prev, documents[docIndex].document_type]))
      
      toast({
        title: "Document Uploaded",
        description: `${documents[docIndex].document_type} uploaded successfully`,
      })
    } catch (error) {
      console.error('Error uploading document:', error)

      let errorMessage = "Failed to upload document"
      if (error instanceof Error) {
        if (error.message.includes('bucket not found')) {
          errorMessage = "Storage bucket not found. Please contact administrator."
        } else if (error.message.includes('permission')) {
          errorMessage = "Permission denied. Please contact administrator."
        } else {
          errorMessage = error.message
        }
      }

      toast({
        title: "Upload Failed",
        description: errorMessage,
        variant: "destructive"
      })
    }
  }

  const handleSkipDocument = async (docIndex: number, reason: string) => {
    try {
      await supabase.rpc('handle_employee_document' as any, {
        p_employee_id: employeeId,
        p_contract_id: contractId,
        p_document_type: documents[docIndex].document_type,
        p_is_skipped: true,
        p_skip_reason: reason
      })

      setCompletedDocs(prev => new Set([...prev, documents[docIndex].document_type]))
      
      toast({
        title: "Document Skipped",
        description: `${documents[docIndex].document_type} skipped`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to skip document",
        variant: "destructive"
      })
    }
  }

  const handleComplete = async () => {
    setLoading(true)
    try {
      await supabase.rpc('complete_document_phase'  as any, {
        p_employee_id: employeeId
      })

      toast({
        title: "Documents Submitted",
        description: "Waiting for HR approval",
      })

      await supabase.auth.signOut()
      navigate('/onboarding/waiting')
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to complete document phase",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const allDocumentsHandled = documents.length > 0 && documents.every(doc => 
    completedDocs.has(doc.document_type)
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 p-4">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Upload className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl">Document Upload</CardTitle>
            <p className="text-gray-600">Upload required documents or skip if not available</p>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {fetchingDocs ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <h3 className="text-xl font-medium mb-2">Loading Required Documents</h3>
                <p className="text-gray-600">Please wait while we fetch your document requirements...</p>
              </div>
            ) : documents.length > 0 ? (
              documents.map((doc, index) => (
                <Card key={doc.document_type} className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-medium">{doc.document_type}</h3>
                      <p className="text-sm text-gray-600">
                        {doc.is_mandatory ? 'Required' : 'Optional'}
                        {doc.remarks && ` • ${doc.remarks}`}
                      </p>
                    </div>
                    {completedDocs.has(doc.document_type) && (
                      <Check className="h-5 w-5 text-green-600" />
                    )}
                  </div>
                  
                  {!completedDocs.has(doc.document_type) && (
                    <div className="space-y-4">
                      <div>
                        <Label>Upload Document</Label>
                        <Input
                          type="file"
                          onChange={(e) => {
                            const file = e.target.files?.[0]
                            if (file) handleFileUpload(index, file)
                          }}
                          accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                        />
                      </div>
                      
                      <div className="border-t pt-4">
                        <Label>Or Skip with Reason</Label>
                        <div className="flex gap-2 mt-2">
                          <Textarea
                            placeholder="Reason for skipping..."
                            value={doc.skipReason}
                            onChange={(e) => {
                              const updated = [...documents]
                              updated[index].skipReason = e.target.value
                              setDocuments(updated)
                            }}
                            className="flex-1"
                          />
                          <Button
                            variant="outline"
                            onClick={() => handleSkipDocument(index, doc.skipReason)}
                            disabled={!doc.skipReason.trim()}
                          >
                            <SkipForward className="h-4 w-4 mr-2" />
                            Skip
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-xl font-medium mb-2">No Documents Required</h3>
                <p className="text-gray-600 mb-6">This contract type doesn't require any document uploads.</p>
                <Button
                  onClick={handleComplete}
                  disabled={loading}
                  className="px-8 bg-blue-600 hover:bg-blue-700"
                >
                  {loading ? "Processing..." : "Continue to Approval"}
                </Button>
              </div>
            )}
            
            {documents.length > 0 && allDocumentsHandled && (
              <div className="text-center pt-6">
                <Button
                  onClick={handleComplete}
                  disabled={loading}
                  className="px-8 bg-blue-600 hover:bg-blue-700"
                >
                  {loading ? "Submitting..." : "Complete & Submit for Approval"}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}