import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom"
import { <PERSON><PERSON>ef<PERSON>, ArrowRight, Save, Check, User, Mail, Briefcase, CreditCard, Clock, Shield } from "lucide-react"
import { Layout } from "@/components/Layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"

// Step Components
import PersonalInfoStep from "@/components/employee/PersonalInfoStep"
import AuthenticationStep from "@/components/employee/AuthenticationStep"
import WorkDetailsStep from "@/components/employee/WorkDetailsStep"
import BankingInfoStep from "@/components/employee/BankingInfoStep"
import WorkScheduleStep from "@/components/employee/WorkScheduleStep"
import SystemAccessStep from "@/components/employee/SystemAccessStep"
import { usePermissions } from "@/hooks/usePermissions";
interface FormData {
  personalInfo: {
    first_name: string
    middle_name: string
    last_name: string
    personal_email: string
    phone: string
    date_of_birth: Date | null
    gender: string
    address: string
    city: string
    state: string
    pincode: string
    biometric_code: string
  }
  authDetails: {
    company_email: string
    password: string
    employee_code: string
    hire_date: Date | null
    department_id: string
    designation_id: string
  }
  workDetails: {
    employment_status: string
    onboarding_status: string
  }
  bankingInfo: {
    bank_name: string
    account_holder_name: string
    account_number: string
    ifsc_code: string
    branch_name: string
    is_primary: boolean
  }
  workSchedule: {
    shift_id: string
    work_week_id: string
  }
  systemAccess: {
    role_id: string
    is_active: boolean
  }
}

const steps = [
  { id: 1, title: "Personal Info", icon: User, description: "Basic personal details" },
  { id: 2, title: "Authentication", icon: Mail, description: "Login credentials" },
  { id: 3, title: "Work Details", icon: Briefcase, description: "Employment information" },
  { id: 4, title: "Banking Info", icon: CreditCard, description: "Bank account details" },
  { id: 5, title: "Work Schedule", icon: Clock, description: "Shifts and work week" },
  { id: 6, title: "System Access", icon: Shield, description: "Roles and permissions" },
]

export default function EditEmployee() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const { hasPermission, loading: permissionsLoading } = usePermissions();




  const [formData, setFormData] = useState<FormData>({
    personalInfo: {
      first_name: "",
      middle_name: "",
      last_name: "",
      personal_email: "",
      phone: "",
      date_of_birth: null,
      gender: "",
      address: "",
      city: "",
      state: "",
      pincode: "",
      biometric_code: ""
    },
    authDetails: {
      company_email: "",
      password: "",
      employee_code: "",
      hire_date: null,
      department_id: "",
      designation_id: ""
    },
    workDetails: {
      employment_status: "active",
      onboarding_status: "approved"
    },
    bankingInfo: {
      bank_name: "",
      account_holder_name: "",
      account_number: "",
      ifsc_code: "",
      branch_name: "",
      is_primary: true
    },
    workSchedule: {
      shift_id: "",
      work_week_id: ""
    },
    systemAccess: {
      role_id: "",
      is_active: true
    }
  })
  const [originalData, setOriginalData] = useState<FormData | null>(null)

  useEffect(() => {
    if (id) {
      fetchEmployee()
    }
  }, [id])

  const fetchEmployee = async () => {
    try {
      // Get employee with all related data
      const { data: employee, error } = await supabase
        .from('employees')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error

      // Get user profile
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', employee.user_id)
        .single()

      // Get bank details
      const { data: bankDetails } = await supabase
        .from('employee_bank_details')
        .select('*')
        .eq('employee_id', id)
        .eq('is_primary', true)
        .single()

      // Get employee shift
      const { data: employeeShift } = await supabase
        .from('employee_shifts')
        .select('shift_id, work_week_id')
        .eq('employee_id', id)
        .eq('is_active', true)
        .single()

      // Get user role
      const { data: userRole } = await supabase
        .from('user_roles')
        .select('role_id')
        .eq('user_id', employee.user_id)
        .eq('is_active', true)
        .single()

      // Populate form data
      const initialData = {
        personalInfo: {
          first_name: profile?.first_name || "",
          middle_name: profile?.middle_name || "",
          last_name: profile?.last_name || "",
          personal_email: profile?.personal_email || "",
          phone: profile?.phone || "",
          date_of_birth: profile?.date_of_birth ? new Date(profile.date_of_birth) : null,
          gender: profile?.gender || "",
          address: profile?.address || "",
          city: profile?.city || "",
          state: profile?.state || "",
          pincode: profile?.pincode || "",
          biometric_code: profile?.biometric_code || ""
        },
        authDetails: {
          company_email: employee.company_email || "",
          password: "", // Don't populate password
          employee_code: employee.employee_code || "",
          hire_date: employee.hire_date ? new Date(employee.hire_date) : null,
          department_id: employee.department_id || "",
          designation_id: employee.designation_id || ""
        },
        workDetails: {
          employment_status: employee.employment_status || "active",
          onboarding_status: employee.onboarding_status || "approved"
        },
        bankingInfo: {
          bank_name: bankDetails?.bank_name || "",
          account_holder_name: bankDetails?.account_holder_name || "",
          account_number: bankDetails?.account_number || "",
          ifsc_code: bankDetails?.ifsc_code || "",
          branch_name: bankDetails?.branch_name || "",
          is_primary: bankDetails?.is_primary || true
        },
        workSchedule: {
          shift_id: employeeShift?.shift_id || "",
          work_week_id: employeeShift?.work_week_id || ""
        },
        systemAccess: {
          role_id: userRole?.role_id || "",
          is_active: employee.is_active || true
        }
      }
      setFormData(initialData)
      setOriginalData(initialData)

      // Mark all steps as completed for edit mode
      setCompletedSteps([1, 2, 3, 4, 5, 6])
    } catch (error) {
      console.error('Error fetching employee:', error)
      toast({
        title: "Error",
        description: "Failed to fetch employee details",
        variant: "destructive",
      })
      navigate('/employees')
    } finally {
      setLoading(false)
    }
  }

  const updateFormData = (section: keyof FormData, data: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: { ...prev[section], ...data }
    }))
  }

  const handleSubmit = async () => {
    setSaving(true)
    try {
      if (!originalData) throw new Error('Original data not loaded')

      const { data: employee } = await supabase
        .from('employees')
        .select('user_id')
        .eq('id', id)
        .single()

      if (!employee) throw new Error('Employee not found')

      // Check and update user profile if personal info changed
      const personalChanged = JSON.stringify(formData.personalInfo) !== JSON.stringify(originalData.personalInfo)
      if (personalChanged) {
        const { error: profileError } = await supabase
          .from('user_profiles')
          .update({
            first_name: formData.personalInfo.first_name,
            middle_name: formData.personalInfo.middle_name || null,
            last_name: formData.personalInfo.last_name,
            personal_email: formData.personalInfo.personal_email,
            phone: formData.personalInfo.phone || null,
            date_of_birth: formData.personalInfo.date_of_birth?.toISOString().split('T')[0] || null,
            gender: (formData.personalInfo.gender as "male" | "female" | "other") || null,
            city: formData.personalInfo.city || null,
            state: formData.personalInfo.state || null,
            address: formData.personalInfo.address || null,
            pincode: formData.personalInfo.pincode || null,
            biometric_code: formData.personalInfo.biometric_code || null
          })
          .eq('id', employee.user_id)
        if (profileError) throw profileError
      }

      // Check and update employee table if auth/work details changed
      const authChanged = JSON.stringify(formData.authDetails) !== JSON.stringify(originalData.authDetails)
      const workChanged = JSON.stringify(formData.workDetails) !== JSON.stringify(originalData.workDetails)
      const accessChanged = formData.systemAccess.is_active !== originalData.systemAccess.is_active
      
      if (authChanged || workChanged || accessChanged) {
        const { error: employeeError } = await supabase
          .from('employees')
          .update({
            company_email: formData.authDetails.company_email,
            employee_code: formData.authDetails.employee_code,
            department_id: formData.authDetails.department_id || null,
            designation_id: formData.authDetails.designation_id || null,
            hire_date: formData.authDetails.hire_date?.toISOString().split('T')[0],
            employment_status: formData.workDetails.employment_status as "active" | "terminated" | "inactive" | "on_leave",
            onboarding_status: formData.workDetails.onboarding_status as "approved" | "pending" | "contract_accepted" | "docs_uploaded",
            is_active: formData.systemAccess.is_active
          })
          .eq('id', id)
        if (employeeError) throw employeeError
      }

      // Check and update bank details if changed
      const bankChanged = JSON.stringify(formData.bankingInfo) !== JSON.stringify(originalData.bankingInfo)
      if (bankChanged && formData.bankingInfo.bank_name && formData.bankingInfo.account_number) {
        const { error: bankError } = await supabase
          .from('employee_bank_details')
          .upsert({
            employee_id: id,
            bank_name: formData.bankingInfo.bank_name,
            account_holder_name: formData.bankingInfo.account_holder_name,
            account_number: formData.bankingInfo.account_number,
            ifsc_code: formData.bankingInfo.ifsc_code,
            branch_name: formData.bankingInfo.branch_name || null,
            is_primary: formData.bankingInfo.is_primary
          })
        if (bankError) throw bankError
      }

      // Check and update work schedule if changed
      const scheduleChanged = JSON.stringify(formData.workSchedule) !== JSON.stringify(originalData.workSchedule)
      if (scheduleChanged && formData.workSchedule.shift_id && formData.workSchedule.work_week_id) {
        const { error: shiftError } = await supabase
          .from('employee_shifts')
          .update({
            shift_id: formData.workSchedule.shift_id,
            work_week_id: formData.workSchedule.work_week_id
          })
          .eq('employee_id', id)
          .eq('is_active', true)
        if (shiftError) throw shiftError
      }

      // Check and update user role if changed
      const roleChanged = formData.systemAccess.role_id !== originalData.systemAccess.role_id
      if (roleChanged && formData.systemAccess.role_id) {
        const { error: roleError } = await supabase
          .from('user_roles')
          .update({ role_id: formData.systemAccess.role_id })
          .eq('user_id', employee.user_id)
        if (roleError) throw roleError
      }

      toast({
        title: "Success",
        description: "Employee updated successfully",
      })
      navigate('/employees')
    } catch (error: any) {
      console.error('Error updating employee:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to update employee",
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <PersonalInfoStep data={formData.personalInfo} onChange={(data) => updateFormData('personalInfo', data)} />
      case 2:
        return <AuthenticationStep data={formData.authDetails} onChange={(data) => updateFormData('authDetails', data)} />
      case 3:
        return <WorkDetailsStep data={formData.workDetails} onChange={(data) => updateFormData('workDetails', data)} />
      case 4:
        return <BankingInfoStep data={formData.bankingInfo} onChange={(data) => updateFormData('bankingInfo', data)} />
      case 5:
        return <WorkScheduleStep data={formData.workSchedule} onChange={(data) => updateFormData('workSchedule', data)} />
      case 6:
        return <SystemAccessStep data={formData.systemAccess} onChange={(data) => updateFormData('systemAccess', data)} />
      default:
        return null
    }
  }

  const progress = (currentStep / 6) * 100
  if (permissionsLoading || loading) {
    return <Layout><div>Loading...</div></Layout>;
  }

  // 👇 ADD THIS PAGE GUARD AT THE TOP 👇
  if (!hasPermission('employees', 'edit')) {
    return (
      <Layout>
        <div className="p-8 text-center text-red-600">
          <h2>Access Denied</h2>
          <p>You do not have permission to edit employees.</p>
        </div>
      </Layout>
    );
  }
  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading employee details...</div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="mx-auto space-y-1 p-2 md:p-2">
        {/* Header */}
        <div className="flex flex-col sm:flex-row bg-[#4A9782] justify-between items-start sm:items-center gap-4 dark:bg-gray-800 rounded-lg p-6 text-white shadow-sm">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate('/employees')} className="hidden sm:flex">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl md:text-xl font-bold tracking-tight">Edit Employee</h1>
              <p className="text-muted-foreground mt-1 text-white">Update employee information</p>
            </div>
          </div>
          <Button variant="outline" onClick={() => navigate('/employees')} className="sm:hidden w-full">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Employees
          </Button>
        </div>

        <div className="space-y-6">
          {/* Progress Bar */}
          <Card className="border-0 shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm font-medium text-gray-700">Step {currentStep} of 6</span>
                <span className="text-sm text-gray-500">{Math.round(progress)}% Complete</span>
              </div>
              <Progress value={progress} className="h-2" />
            </CardContent>
          </Card>

          {/* Step Navigation */}
          <Card className="border-0 shadow-md">
            <CardContent className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                {steps.map((step) => {
                  const Icon = step.icon
                  const isCompleted = completedSteps.includes(step.id)
                  const isCurrent = currentStep === step.id
                  
                  return (
                    <div
                      key={step.id}
                      className={`p-3 rounded-lg border text-center cursor-pointer transition-all ${
                        isCurrent
                          ? 'bg-blue-50 border-blue-200 text-blue-700'
                          : isCompleted
                          ? 'bg-green-50 border-green-200 text-green-700'
                          : 'bg-white border-gray-200 text-gray-500 hover:bg-gray-50'
                      }`}
                      onClick={() => setCurrentStep(step.id)}
                    >
                      <div className="flex items-center justify-center mb-2">
                        {isCompleted ? (
                          <Check className="h-5 w-5" />
                        ) : (
                          <Icon className="h-5 w-5" />
                        )}
                      </div>
                      <div className="text-xs font-medium">{step.title}</div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Step Content */}
          <Card className="border-0 shadow-md">
            <CardHeader className="border-b bg-muted/30 rounded-t-lg">
              <CardTitle className="text-lg font-semibold">{steps[currentStep - 1].title}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              {renderStepContent()}
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <Card className="border-0 shadow-md">
            <CardContent className="flex justify-between py-4">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
                disabled={currentStep === 1}
                className="px-6"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex gap-3">
                {currentStep === 6 ? (
                  <Button
                    onClick={handleSubmit}
                    disabled={saving}
                    className="px-8 bg-primary hover:bg-primary/90"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {saving ? "Updating..." : "Update Employee"}
                  </Button>
                ) : (
                  <Button onClick={() => setCurrentStep(Math.min(6, currentStep + 1))} className="px-6">
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  )
}