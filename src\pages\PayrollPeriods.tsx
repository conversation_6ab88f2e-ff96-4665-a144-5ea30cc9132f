import { useState, useEffect } from "react"
import { Plus, Calendar, Edit, Trash2, CheckCircle } from "lucide-react"
import { Layout } from "@/components/Layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"

interface PayrollPeriod {
  id: string
  name: string
  start_date: string
  end_date: string
  year: number
  month: number
  status: 'draft' | 'processed' | 'confirmed'
  created_at: string
}

export default function PayrollPeriods() {
  const { toast } = useToast()
  const [periods, setPeriods] = useState<PayrollPeriod[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    start_date: "",
    end_date: ""
  })

  useEffect(() => {
    fetchPeriods()
  }, [])

  useEffect(() => {
    if (formData.year && formData.month) {
      const startDate = new Date(formData.year, formData.month - 1, 1)
      const endDate = new Date(formData.year, formData.month, 0)
      
      setFormData(prev => ({
        ...prev,
        name: `${getMonthName(formData.month)} ${formData.year}`,
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0]
      }))
    }
  }, [formData.year, formData.month])

  const fetchPeriods = async () => {
    try {
      const { data, error } = await supabase
        .from('payroll_periods')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false)
        .order('year', { ascending: false })
        .order('month', { ascending: false })

      if (error) throw error
      setPeriods(data || [])
    } catch (error) {
      console.error('Error fetching periods:', error)
      toast({ title: "Error", description: "Failed to fetch payroll periods", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  const getMonthName = (month: number) => {
    const months = ['January', 'February', 'March', 'April', 'May', 'June',
                   'July', 'August', 'September', 'October', 'November', 'December']
    return months[month - 1]
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const { error } = await supabase
        .from('payroll_periods')
        .insert({
          name: formData.name,
          start_date: formData.start_date,
          end_date: formData.end_date,
          year: formData.year,
          month: formData.month,
          status: 'draft'
        })

      if (error) throw error

      toast({ title: "Success", description: "Payroll period created successfully" })
      setDialogOpen(false)
      setFormData({
        name: "",
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        start_date: "",
        end_date: ""
      })
      fetchPeriods()
    } catch (error: any) {
      console.error('Error creating period:', error)
      toast({ title: "Error", description: error.message, variant: "destructive" })
    }
  }

  const updateStatus = async (id: string, status: 'processed' | 'confirmed') => {
    try {
      const { error } = await supabase
        .from('payroll_periods')
        .update({ status })
        .eq('id', id)

      if (error) throw error

      toast({ title: "Success", description: `Period ${status} successfully` })
      fetchPeriods()
    } catch (error) {
      console.error('Error updating status:', error)
      toast({ title: "Error", description: "Failed to update status", variant: "destructive" })
    }
  }

  const getStatusBadge = (status: string) => {
    const styles = {
      draft: "bg-yellow-100 text-yellow-800",
      processed: "bg-blue-100 text-blue-800", 
      confirmed: "bg-green-100 text-green-800"
    }
    return <Badge className={styles[status as keyof typeof styles]}>{status}</Badge>
  }

  return (
    <Layout>
      <div className="space-y-6">
        <Card>
          <CardHeader className="bg-gray-50 border-b">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Payroll Periods</CardTitle>
                <p className="text-muted-foreground mt-1 text-sm">Manage monthly payroll cycles</p>
              </div>
              <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Period
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create Payroll Period</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Year</Label>
                        <Select value={formData.year.toString()} onValueChange={(value) => setFormData({...formData, year: parseInt(value)})}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({length: 5}, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                              <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Month</Label>
                        <Select value={formData.month.toString()} onValueChange={(value) => setFormData({...formData, month: parseInt(value)})}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({length: 12}, (_, i) => i + 1).map(month => (
                              <SelectItem key={month} value={month.toString()}>{getMonthName(month)}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div>
                      <Label>Period Name</Label>
                      <Input value={formData.name} onChange={(e) => setFormData({...formData, name: e.target.value})} />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Start Date</Label>
                        <Input type="date" value={formData.start_date} onChange={(e) => setFormData({...formData, start_date: e.target.value})} />
                      </div>
                      <div>
                        <Label>End Date</Label>
                        <Input type="date" value={formData.end_date} onChange={(e) => setFormData({...formData, end_date: e.target.value})} />
                      </div>
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>Cancel</Button>
                      <Button type="submit">Create Period</Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Period</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {periods.map(period => (
                  <TableRow key={period.id}>
                    <TableCell className="font-medium">{period.name}</TableCell>
                    <TableCell>
                      {format(new Date(period.start_date), 'MMM dd')} - {format(new Date(period.end_date), 'MMM dd, yyyy')}
                    </TableCell>
                    <TableCell>{getStatusBadge(period.status)}</TableCell>
                    <TableCell>{format(new Date(period.created_at), 'MMM dd, yyyy')}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {period.status === 'draft' && (
                          <Button size="sm" onClick={() => updateStatus(period.id, 'processed')}>
                            Process
                          </Button>
                        )}
                        {period.status === 'processed' && (
                          <Button size="sm" onClick={() => updateStatus(period.id, 'confirmed')}>
                            <CheckCircle className="mr-1 h-3 w-3" />
                            Confirm
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </Layout>
  )
}