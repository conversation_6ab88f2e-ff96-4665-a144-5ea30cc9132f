import { useState, useEffect } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { FileText, Check, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/integrations/supabase/client"

export default function ContractAcceptance() {
  const navigate = useNavigate()
  const location = useLocation()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  
  const contractData = location.state?.contractData

  useEffect(() => {
    if (!contractData) {
      navigate('/dashboard')
    }
  }, [contractData, navigate])

  const handleAccept = async () => {
    setLoading(true)
    try {
      const { error } = await supabase.rpc('accept_employee_contract' as any, {
        p_employee_id: contractData.employee_id,
        p_contract_id: contractData.contract_id,
        p_ip_address: null,
        p_user_agent: navigator.userAgent
      })

      if (error) throw error

      // Fetch required documents after contract acceptance
      const { data: requiredDocsData, error: docsError } = await supabase
        .from('contracts')
        .select(`
          contract_type_required_documents!inner(
            document_type,
            is_mandatory,
            remarks
          )
        `)
        .eq('id', contractData.contract_id)
        .eq('contract_type_required_documents.is_active', true)
        .eq('contract_type_required_documents.is_deleted', false)

      if (docsError) {
        console.error('Error fetching required documents:', docsError)
        // Continue with empty array if there's an error
      }

      // Extract the required documents from the nested structure
      const requiredDocs = requiredDocsData?.[0]?.contract_type_required_documents || []

      toast({
        title: "Contract Accepted",
        description: "Proceeding to document upload",
      })

      navigate('/onboarding/documents', {
        state: {
          employeeId: contractData.employee_id,
          contractId: contractData.contract_id,
          requiredDocs: requiredDocs
        }
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleReject = async () => {
    await supabase.auth.signOut()
    navigate('/login')
  }

  if (!contractData) return null

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="h-8 w-8 text-blue-600" />
          </div>
          <CardTitle className="text-2xl">Employment Contract</CardTitle>
          <p className="text-gray-600">Please review and accept your employment contract</p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg max-h-96 overflow-y-auto">
            <div className="prose prose-sm max-w-none">
              {contractData.contract_template ? (
                <div dangerouslySetInnerHTML={{ __html: contractData.contract_template }} />
              ) : (
                <p>Contract template content will be displayed here.</p>
              )}
            </div>
          </div>
          
          <div className="flex gap-4 justify-center">
            <Button
              variant="outline"
              onClick={handleReject}
              className="px-8"
            >
              <X className="h-4 w-4 mr-2" />
              Reject
            </Button>
            
            <Button
              onClick={handleAccept}
              disabled={loading}
              className="px-8 bg-green-600 hover:bg-green-700"
            >
              <Check className="h-4 w-4 mr-2" />
              {loading ? "Processing..." : "I Agree"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}