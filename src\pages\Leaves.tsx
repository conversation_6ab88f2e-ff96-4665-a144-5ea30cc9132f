import { useState, useEffect, useMemo } from "react"
import { Plus, Search, Eye, Check, X, Calendar, User, FileText, AlertCircle, Loader2 } from "lucide-react"
import { Layout } from "@/components/Layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>rigger } from "@/components/ui/tabs"
import { supabase } from "@/integrations/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/AuthContext"
import { format, differenceInCalendarDays } from "date-fns"

// Interface definitions based on your schema
interface LeaveType {
  id: string
  name: string
  code: string
}

interface Employee {
  id: string
  employee_code: string
  user_profiles: {
    first_name: string
    last_name: string
  }
}

interface ContractLeave {
  id: string
  leave_type_id: string
  leave_types: LeaveType
}

interface LeaveRequest {
  id: string
  employee_id: string
  leave_type_id: string
  start_date: string
  end_date: string
  total_days: number
  half_day_type: 'first_half' | 'second_half' | null
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  applied_at: string
  approved_at: string | null
  rejection_reason: string | null
  emergency_contact: string | null
  employees: Employee
  leave_types: LeaveType
}

interface LeaveBalance {
  id: string
  leave_type_id: string
  year: number
  allocated_days: number
  used_days: number
  carried_forward: number
  encashed_days: number
  remaining_days: number
  leave_types: LeaveType
}

// --- Component Starts Here ---

export default function LeavesPage() {
  const { toast } = useToast()
  const { user, isHRManager, isAdmin } = useAuth()
  const isManager = isHRManager || isAdmin

  // --- State Management ---
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("requests")

  // Data states
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([])
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([])
  const [myContractLeaves, setMyContractLeaves] = useState<ContractLeave[]>([])
  const [currentEmployeeId, setCurrentEmployeeId] = useState<string | null>(null)
  
  // Admin-specific state
  const [allEmployees, setAllEmployees] = useState<Employee[]>([])
  
  // Dialog & Form states
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false)
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null)
  const [rejectionReason, setRejectionReason] = useState('')

const [formState, setFormState] = useState({
  employee_id: "",
  leave_type_id: "",
  start_date: "",
  end_date: "",
  half_day_type: "full_day", // 👈 Change this from ""
  reason: "",
  emergency_contact: "",
});
  const [formAvailableLeaves, setFormAvailableLeaves] = useState<ContractLeave[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)


  // --- Data Fetching ---
  useEffect(() => {
    fetchData()
  }, [isManager, user])

  const fetchData = async () => {
    if (!user) return
    setLoading(true)
    try {
      let employeeId: string | null = null

      // Step 1: Identify the employee (for non-managers)
      if (!isManager) {
        const { data: empData, error: empError } = await supabase
          .from('employees')
          .select('id')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .eq('is_deleted', false)
          .maybeSingle()

        if (empError || !empData) throw new Error("Could not find your employee profile.")
        employeeId = empData.id
        setCurrentEmployeeId(employeeId)
      }

      // Step 2: Fetch data based on permission
      if (isManager) {
        // Admins fetch all requests and all employees
        const { data: requests, error: reqErr } = await fetchAllLeaveRequests()
        if (reqErr) throw reqErr
        
        // Get user profiles for employees
        const employeeUserIds = requests?.map(req => req.employees.user_id) || []
        const { data: profiles } = await supabase
          .from('user_profiles')
          .select('id, first_name, last_name')
          .in('id', employeeUserIds)
        
        // Combine requests with user profiles
        const requestsWithProfiles = requests?.map(req => ({
          ...req,
          employees: {
            ...req.employees,
            user_profiles: profiles?.find(p => p.id === req.employees.user_id) || {
              first_name: '',
              last_name: ''
            }
          }
        })) || []
        
        setLeaveRequests(requestsWithProfiles)
        
        // Fetch all employees for the form
        const { data: employees, error: empErr } = await fetchAllEmployees()
        if (empErr) throw empErr
        
        // Get user profiles for all employees
        const allEmployeeUserIds = employees?.map(emp => emp.user_id) || []
        const { data: allProfiles } = await supabase
          .from('user_profiles')
          .select('id, first_name, last_name')
          .in('id', allEmployeeUserIds)
        
        // Combine employees with user profiles
        const employeesWithProfiles = employees?.map(emp => ({
          ...emp,
          user_profiles: allProfiles?.find(p => p.id === emp.user_id) || {
            first_name: '',
            last_name: ''
          }
        })) || []
        
        setAllEmployees(employeesWithProfiles)

      } else if (employeeId) {
        // Employees fetch their own data
        const { data: requests, error: reqErr } = await fetchMyLeaveRequests(employeeId)
        if (reqErr) throw reqErr
        
        // Get user profile for current employee
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('id, first_name, last_name')
          .eq('id', user.id)
          .maybeSingle()
        
        // Combine requests with user profile
        const requestsWithProfile = requests?.map(req => ({
          ...req,
          employees: {
            ...req.employees,
            user_profiles: profile || { first_name: '', last_name: '' }
          }
        })) || []
        
        setLeaveRequests(requestsWithProfile)
        
        const { data: balances, error: balErr } = await fetchMyLeaveBalances(employeeId)
        if (balErr) throw balErr
        setLeaveBalances(balances || [])

        const { data: contractLeaves, error: conErr } = await fetchContractLeavesForEmployee(employeeId)
        if (conErr) throw conErr
        setMyContractLeaves(contractLeaves || [])
        setFormAvailableLeaves(contractLeaves || [])
      }
    } catch (error: any) {
      toast({ title: "Error", description: error.message, variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  // --- Helper Fetch Functions ---
  const fetchAllLeaveRequests = () => supabase
    .from('leave_requests')
    .select(`
      *,
      employees!inner(
        id,
        employee_code,
        user_id
      ),
      leave_types(*)
    `)
    .order('applied_at', { ascending: false })

  const fetchAllEmployees = () => supabase
    .from('employees')
    .select(`
      id,
      employee_code,
      user_id
    `)
    .eq('is_active', true)
    .eq('is_deleted', false)
    .order('employee_code')

  const fetchMyLeaveRequests = (employeeId: string) => supabase
    .from('leave_requests')
    .select(`
      *,
      employees!inner(
        id,
        employee_code,
        user_id
      ),
      leave_types(*)
    `)
    .eq('employee_id', employeeId)
    .order('applied_at', { ascending: false })

  const fetchMyLeaveBalances = (employeeId: string) => supabase
    .from('leave_balances')
    .select(`*, leave_types(*)`)
    .eq('employee_id', employeeId)
    .eq('year', new Date().getFullYear())

  const fetchContractLeavesForEmployee = async (employeeId: string) => {
    const { data: contractData, error: contractError } = await supabase
      .from('contracts')
      .select('id')
      .eq('employee_id', employeeId)
      .eq('status', 'active')
      .eq('is_active', true)
      .eq('is_deleted', false)
      .maybeSingle()
      
    if (contractError || !contractData) {
      return { data: [], error: contractError || new Error("No active contract found.") }
    }
    
    // Initialize leave balances if they don't exist
    const { error: syncError } = await supabase.rpc('sync_leave_balances_from_contract' as any, {
      p_employee_id: employeeId,
      p_contract_id: contractData.id,
      p_year: new Date().getFullYear()
    })
    
    if (syncError) {
      console.warn('Warning: Failed to sync leave balances:', syncError)
    }
    
    return supabase
      .from('contract_leaves')
      .select(`*, leave_types(*)`)
      .eq('contract_id', contractData.id)
      .eq('is_active', true)
      .eq('is_deleted', false)
  }

  // --- Form & Dialog Logic ---
  const handleOpenAddDialog = () => {
    resetForm();
    if (!isManager) {
        setFormState(prev => ({ ...prev, employee_id: currentEmployeeId || ""}));
    }
    setAddDialogOpen(true);
  };
  
const resetForm = () => {
  setFormState({
    employee_id: "", 
    leave_type_id: "", 
    start_date: "", 
    end_date: "", 
    half_day_type: "full_day", // 👈 Change this from ""
    reason: "", 
    emergency_contact: ""
  });
  setFormAvailableLeaves(isManager ? [] : myContractLeaves);
}

  const handleAdminEmployeeSelect = async (employeeId: string) => {
    setFormState(prev => ({ ...prev, employee_id: employeeId, leave_type_id: "" }))
    if (!employeeId) {
      setFormAvailableLeaves([])
      return
    }
    
    const { data, error } = await fetchContractLeavesForEmployee(employeeId)
    if (error) {
      toast({ 
        title: "Error", 
        description: "Could not fetch leave types for this employee.", 
        variant: "destructive" 
      })
      setFormAvailableLeaves([])
    } else {
      setFormAvailableLeaves(data || [])
    }
    
    // Fetch leave balances for selected employee
    const { data: balances } = await supabase
      .from('leave_balances')
      .select('*, leave_types(*)')
      .eq('employee_id', employeeId)
      .eq('year', new Date().getFullYear())
    
    setLeaveBalances(balances || [])
  }

  // --- Form Submission ---
const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const { employee_id, leave_type_id, start_date, end_date, reason, half_day_type } = formState;

    if (!employee_id || !leave_type_id || !start_date || !end_date || !reason) {
        toast({ title: "Missing Fields", description: "Please fill all required fields.", variant: "destructive" });
        setIsSubmitting(false);
        return;
    }
    
    // Calculate total days
    let total_days = 0;
    if (half_day_type === 'first_half' || half_day_type === 'second_half') {
        total_days = 0.5;
        if(start_date !== end_date) {
            toast({ title: "Invalid Date", description: "For half-day leave, start and end date must be the same.", variant: "destructive" });
            setIsSubmitting(false);
            return;
        }
    } else {
        total_days = differenceInCalendarDays(new Date(end_date), new Date(start_date)) + 1;
    }

    if(total_days <= 0) {
        toast({ title: "Invalid Dates", description: "End date must be on or after the start date.", variant: "destructive" });
        setIsSubmitting(false);
        return;
    }

    try {
        // Check leave balance
        const { data: balance } = await supabase
          .from('leave_balances')
          .select('remaining_days')
          .eq('employee_id', employee_id)
          .eq('leave_type_id', leave_type_id)
          .eq('year', new Date(start_date).getFullYear())
          .maybeSingle();
          
        if (balance && balance.remaining_days < total_days) {
            throw new Error(`Insufficient leave balance. Available: ${balance.remaining_days} days, Requested: ${total_days} days.`);
        }
        
         // 1. Define a type for the data to be inserted
        type LeaveRequestInsert = {
          employee_id: string;
          leave_type_id: string;
          start_date: string;
          end_date: string;
          reason: string;
          emergency_contact: string | null;
          half_day_type: 'first_half' | 'second_half' | null; // This is the key change
          total_days: number;
          status: 'pending' | 'approved' | 'rejected' | 'cancelled';
          created_by?: string;
        }

        const dataToInsert: LeaveRequestInsert = {
            ...formState,
            half_day_type: half_day_type === 'full_day' ? null : half_day_type as 'first_half' | 'second_half',
            total_days,
            status: isManager ? 'approved' : 'pending',
            created_by: user?.id,
            emergency_contact: formState.emergency_contact || null
        };
        
        const { error } = await supabase.from('leave_requests').insert(dataToInsert);
        if (error) throw error;
        
        // If admin auto-approved, update balance
        if(isManager) {
            await updateLeaveBalance(employee_id, leave_type_id, start_date, total_days);
        }

        toast({ title: "Success", description: "Leave request submitted successfully." });
        setAddDialogOpen(false);
        fetchData();
    } catch (error: any) {
        toast({ title: "Submission Error", description: error.message, variant: "destructive" });
    } finally {
        setIsSubmitting(false);
    }
};

  // --- Approval Action ---
  const handleApproval = async (request: LeaveRequest, newStatus: 'approved' | 'rejected') => {
    if (newStatus === 'rejected') {
        setSelectedRequest(request);
        setRejectDialogOpen(true);
        return;
    }

    try {
        const { error: updateError } = await supabase.from('leave_requests').update({
            status: newStatus,
            approved_by: user?.id,
            approved_at: new Date().toISOString()
        }).eq('id', request.id);
        if (updateError) throw updateError;
        
        // Update balance if approved
        if (newStatus === 'approved') {
            await updateLeaveBalance(request.employee_id, request.leave_type_id, request.start_date, request.total_days);
        }

        toast({ title: "Success", description: `Leave request has been ${newStatus}.` });
        fetchData();
    } catch (error: any) {
        toast({ title: "Action Failed", description: error.message, variant: "destructive" });
    }
  };

  const handleRejectConfirm = async () => {
    if (!selectedRequest || !rejectionReason.trim()) {
        toast({ title: "Error", description: "Please provide a reason for rejection.", variant: "destructive" });
        return;
    }

    try {
        const { error: updateError } = await supabase.from('leave_requests').update({
            status: 'rejected',
            rejection_reason: rejectionReason,
            approved_by: user?.id,
            approved_at: new Date().toISOString()
        }).eq('id', selectedRequest.id);
        if (updateError) throw updateError;

        toast({ title: "Success", description: "Leave request has been rejected." });
        setRejectDialogOpen(false);
        setRejectionReason('');
        setSelectedRequest(null);
        fetchData();
    } catch (error: any) {
        toast({ title: "Action Failed", description: error.message, variant: "destructive" });
    }
  };
  
  // --- Balance Update Utility ---
  const updateLeaveBalance = async (employeeId: string, leaveTypeId: string, startDate: string, daysToUse: number) => {
    const year = new Date(startDate).getFullYear();
    
    const { error } = await supabase.rpc('update_leave_balance' as any, {
      p_employee_id: employeeId,
      p_leave_type_id: leaveTypeId,
      p_year: year,
      p_days_to_use: daysToUse
    });
      
    if (error) throw new Error("Failed to update leave balance. Please check and manually adjust if needed.");
  };

  // --- Rendering Helpers ---
  const getStatusBadge = (status: string) => {
    const styles = {
      pending: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
      approved: "bg-green-100 text-green-800 hover:bg-green-200",
      rejected: "bg-red-100 text-red-800 hover:bg-red-200",
      cancelled: "bg-gray-100 text-gray-800 hover:bg-gray-200"
    };
    return <Badge className={`capitalize font-medium ${styles[status as keyof typeof styles] || styles.cancelled}`}>{status}</Badge>;
  };
  
  const filteredRequests = useMemo(() =>
    leaveRequests.filter(req => {
        const emp = req.employees;
        const search = searchTerm.toLowerCase();
        return (
            emp?.user_profiles?.first_name.toLowerCase().includes(search) ||
            emp?.user_profiles?.last_name.toLowerCase().includes(search) ||
            emp?.employee_code.toLowerCase().includes(search) ||
            req.leave_types?.name.toLowerCase().includes(search)
        );
    }), [leaveRequests, searchTerm]);

  if (loading) return <Layout><div className="flex justify-center items-center h-64"><Loader2 className="h-8 w-8 animate-spin" /></div></Layout>;

  return (
    <Layout>
      <div className="space-y-6">
        <Card>
          <CardHeader className="bg-gray-50 border-b">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div>
                <CardTitle>{isManager ? 'Leave Management' : 'My Leaves'}</CardTitle>
                <p className="text-muted-foreground mt-1 text-sm">{isManager ? 'Review and manage all employee leave requests.' : 'Apply for leave and view your balances.'}</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search requests..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="pl-10"/>
                </div>
                <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
                    <DialogTrigger asChild>
                        <Button onClick={handleOpenAddDialog}>
                            <Plus className="mr-2 h-4 w-4" />
                            {isManager ? 'Add Leave' : 'Apply for Leave'}
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[480px]">
                        <DialogHeader>
                            <DialogTitle>{isManager ? 'Add Employee Leave' : 'Apply for Leave'}</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleSubmit} className="grid gap-4 py-4">
                            {isManager && (
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="employee_id" className="text-right">Employee</Label>
                                    <Select value={formState.employee_id} onValueChange={handleAdminEmployeeSelect}>
                                        <SelectTrigger className="col-span-3">
                                            <SelectValue placeholder="Select an employee" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {allEmployees.map(emp => (
                                                <SelectItem key={emp.id} value={emp.id}>
                                                    {emp.user_profiles.first_name} {emp.user_profiles.last_name} ({emp.employee_code})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                             <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="leave_type_id" className="text-right">Leave Type</Label>
                                <Select value={formState.leave_type_id} onValueChange={value => setFormState(prev => ({ ...prev, leave_type_id: value }))} disabled={isManager && !formState.employee_id}>
                                    <SelectTrigger className="col-span-3">
                                        <SelectValue placeholder="Select a leave type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {formAvailableLeaves.length > 0 ? formAvailableLeaves.map(cl => {
                                          const balance = leaveBalances.find(b => b.leave_type_id === cl.leave_type_id)
                                          const remainingDays = balance?.remaining_days || 0
                                          const isDisabled = remainingDays <= 0
                                          
                                          return (
                                            <SelectItem 
                                              key={cl.leave_type_id} 
                                              value={cl.leave_type_id}
                                              disabled={isDisabled}
                                            >
                                              <div className="flex justify-between items-center w-full">
                                                <span className={isDisabled ? 'text-gray-400' : ''}>
                                                  {cl.leave_types.name}
                                                </span>
                                                <span className={`text-xs ml-2 ${isDisabled ? 'text-red-500' : 'text-green-600'}`}>
                                                  {remainingDays} days left
                                                </span>
                                              </div>
                                            </SelectItem>
                                          )
                                        }) : <p className="p-2 text-sm text-muted-foreground">No leave types available.</p>}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="start_date" className="text-right">Start Date</Label>
                                <Input id="start_date" type="date" className="col-span-3" value={formState.start_date} onChange={e => setFormState(prev => ({...prev, start_date: e.target.value}))}/>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="end_date" className="text-right">End Date</Label>
                                <Input id="end_date" type="date" className="col-span-3" value={formState.end_date} onChange={e => setFormState(prev => ({...prev, end_date: e.target.value}))}/>
                            </div>
                  <div className="grid grid-cols-4 items-center gap-4">
    <Label htmlFor="half_day_type" className="text-right">Session</Label>
    {/* 👇 Replace your existing Select component with this one 👇 */}
    <Select 
        value={formState.half_day_type} 
        onValueChange={value => setFormState(prev => ({...prev, half_day_type: value}))}
    >
        <SelectTrigger className="col-span-3">
            <SelectValue placeholder="Select a session" />
        </SelectTrigger>
        <SelectContent>
            <SelectItem value="full_day">Full Day</SelectItem>
            <SelectItem value="first_half">First Half</SelectItem>
            <SelectItem value="second_half">Second Half</SelectItem>
        </SelectContent>
    </Select>
</div>
                            <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="reason" className="text-right">Reason</Label>
                                <Textarea id="reason" className="col-span-3" value={formState.reason} onChange={e => setFormState(prev => ({...prev, reason: e.target.value}))}/>
                            </div>
                             <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="emergency_contact" className="text-right">Emergency Contact</Label>
                                <Input id="emergency_contact" placeholder="(Optional)" className="col-span-3" value={formState.emergency_contact} onChange={e => setFormState(prev => ({...prev, emergency_contact: e.target.value}))}/>
                            </div>
                            <div className="flex justify-end gap-2 mt-4">
                                <Button type="button" variant="ghost" onClick={() => setAddDialogOpen(false)}>Cancel</Button>
                                <Button type="submit" disabled={isSubmitting}>{isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin"/>} Submit</Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
             <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="border-b w-full justify-start rounded-none px-6">
                    <TabsTrigger value="requests">Leave Requests</TabsTrigger>
                    <TabsTrigger value="balances">{isManager ? 'Employee Balances' : 'My Balances'}</TabsTrigger>
                </TabsList>
                <TabsContent value="requests" className="p-6">
                     <div className="rounded-md border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    {isManager && <TableHead>Employee</TableHead>}
                                    <TableHead>Leave Type</TableHead>
                                    <TableHead>Dates</TableHead>
                                    <TableHead>Days</TableHead>
                                    <TableHead>Applied On</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredRequests.length > 0 ? filteredRequests.map(req => (
                                    <TableRow key={req.id}>
                                        {isManager && <TableCell>
                                            <div className="font-medium">{req.employees?.user_profiles.first_name} {req.employees?.user_profiles.last_name}</div>
                                            <div className="text-sm text-muted-foreground">{req.employees?.employee_code}</div>
                                        </TableCell>}
                                        <TableCell>{req.leave_types?.name}</TableCell>
                                        <TableCell>{format(new Date(req.start_date), 'dd MMM yyyy')} - {format(new Date(req.end_date), 'dd MMM yyyy')}</TableCell>
                                        <TableCell>{req.total_days}</TableCell>
                                        <TableCell>{format(new Date(req.applied_at), 'dd MMM yyyy')}</TableCell>
                                        <TableCell>{getStatusBadge(req.status)}</TableCell>
                                        <TableCell className="text-right">
                                            <Button variant="ghost" size="icon" onClick={() => { setSelectedRequest(req); setViewDialogOpen(true); }}>
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                            {isManager && req.status === 'pending' && (
                                                <>
                                                 <Button variant="ghost" size="icon" className="text-green-600 hover:text-green-700" onClick={() => handleApproval(req, 'approved')}>
                                                    <Check className="h-4 w-4"/>
                                                 </Button>
                                                 <Button variant="ghost" size="icon" className="text-red-600 hover:text-red-700" onClick={() => handleApproval(req, 'rejected')}>
                                                    <X className="h-4 w-4"/>
                                                 </Button>
                                                </>
                                            )}
                                        </TableCell>
                                    </TableRow>
                                )) : (
                                    <TableRow>
                                        <TableCell colSpan={isManager ? 7 : 6} className="h-24 text-center">No leave requests found.</TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </div>
                </TabsContent>
                <TabsContent value="balances" className="p-6">
                    {isManager ? (
                        <div className="space-y-4">
                            <div className="flex items-center gap-4 mb-6">
                                <Label>Select Employee:</Label>
                                <Select onValueChange={async (employeeId) => {
                                    if (employeeId) {
                                        const { data: balances } = await supabase
                                            .from('leave_balances')
                                            .select('*, leave_types(*)')
                                            .eq('employee_id', employeeId)
                                            .eq('year', new Date().getFullYear())
                                        setLeaveBalances(balances || [])
                                    } else {
                                        setLeaveBalances([])
                                    }
                                }}>
                                    <SelectTrigger className="w-64">
                                        <SelectValue placeholder="Select an employee" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {allEmployees.map(emp => (
                                            <SelectItem key={emp.id} value={emp.id}>
                                                {emp.user_profiles.first_name} {emp.user_profiles.last_name} ({emp.employee_code})
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                {leaveBalances.map(bal => (
                                    <Card key={bal.id} className={bal.remaining_days <= 0 ? 'border-red-200 bg-red-50' : bal.remaining_days <= 2 ? 'border-yellow-200 bg-yellow-50' : ''}>
                                        <CardHeader>
                                            <CardTitle className="flex justify-between items-center">
                                                <span>{bal.leave_types?.name}</span>
                                                {bal.remaining_days <= 0 && <Badge variant="destructive" className="text-xs">Exhausted</Badge>}
                                                {bal.remaining_days > 0 && bal.remaining_days <= 2 && <Badge variant="secondary" className="text-xs">Low</Badge>}
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="text-sm space-y-2">
                                            <div className="flex justify-between"><span>Allocated</span> <span className="font-medium">{bal.allocated_days}</span></div>
                                            <div className="flex justify-between"><span>Carried Forward</span> <span className="font-medium text-blue-600">{bal.carried_forward}</span></div>
                                            <div className="flex justify-between"><span>Used</span> <span className="font-medium text-red-600">{bal.used_days}</span></div>
                                            <div className="flex justify-between"><span>Encashed</span> <span className="font-medium text-purple-600">{bal.encashed_days}</span></div>
                                            <div className="flex justify-between border-t pt-2 mt-2 font-semibold">
                                                <span>Remaining</span> 
                                                <span className={bal.remaining_days <= 0 ? 'text-red-600' : bal.remaining_days <= 2 ? 'text-yellow-600' : 'text-green-600'}>
                                                    {bal.remaining_days}
                                                </span>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                                {leaveBalances.length === 0 && <p className="text-muted-foreground col-span-full text-center">Select an employee to view leave balances.</p>}
                            </div>
                        </div>
                    ) : (
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            {leaveBalances.map(bal => (
                                <Card key={bal.id} className={bal.remaining_days <= 0 ? 'border-red-200 bg-red-50' : bal.remaining_days <= 2 ? 'border-yellow-200 bg-yellow-50' : ''}>
                                    <CardHeader>
                                        <CardTitle className="flex justify-between items-center">
                                            <span>{bal.leave_types?.name}</span>
                                            {bal.remaining_days <= 0 && <Badge variant="destructive" className="text-xs">Exhausted</Badge>}
                                            {bal.remaining_days > 0 && bal.remaining_days <= 2 && <Badge variant="secondary" className="text-xs">Low</Badge>}
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="text-sm space-y-2">
                                        <div className="flex justify-between"><span>Allocated</span> <span className="font-medium">{bal.allocated_days}</span></div>
                                        <div className="flex justify-between"><span>Carried Forward</span> <span className="font-medium text-blue-600">{bal.carried_forward}</span></div>
                                        <div className="flex justify-between"><span>Used</span> <span className="font-medium text-red-600">{bal.used_days}</span></div>
                                        <div className="flex justify-between"><span>Encashed</span> <span className="font-medium text-purple-600">{bal.encashed_days}</span></div>
                                        <div className="flex justify-between border-t pt-2 mt-2 font-semibold">
                                            <span>Remaining</span> 
                                            <span className={bal.remaining_days <= 0 ? 'text-red-600' : bal.remaining_days <= 2 ? 'text-yellow-600' : 'text-green-600'}>
                                                {bal.remaining_days}
                                            </span>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                            {leaveBalances.length === 0 && <p className="text-muted-foreground col-span-full text-center">No leave balances to display for this year.</p>}
                        </div>
                    )}
                </TabsContent>
             </Tabs>
          </CardContent>
        </Card>

        <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Leave Request Details</DialogTitle>
            </DialogHeader>
            {selectedRequest && (
              <div className="grid gap-4 py-4 text-sm">
                {isManager && <div className="grid grid-cols-3 gap-2">
                    <span className="font-semibold text-muted-foreground">Employee</span>
                    <span className="col-span-2">{selectedRequest.employees.user_profiles.first_name} {selectedRequest.employees.user_profiles.last_name}</span>
                </div>}
                <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-muted-foreground">Leave Type</span>
                  <span className="col-span-2">{selectedRequest.leave_types.name}</span>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-muted-foreground">Dates</span>
                  <span className="col-span-2">{format(new Date(selectedRequest.start_date), 'PPP')} to {format(new Date(selectedRequest.end_date), 'PPP')}</span>
                </div>
                 <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-muted-foreground">Total Days</span>
                  <span className="col-span-2">{selectedRequest.total_days} {selectedRequest.half_day_type && `(${selectedRequest.half_day_type.replace('_', ' ')})`}</span>
                </div>
                 <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-muted-foreground">Status</span>
                  <span className="col-span-2">{getStatusBadge(selectedRequest.status)}</span>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-muted-foreground">Reason</span>
                  <p className="col-span-2 leading-relaxed">{selectedRequest.reason}</p>
                </div>
                {selectedRequest.rejection_reason && <div className="grid grid-cols-3 gap-2">
                  <span className="font-semibold text-muted-foreground">Rejection Reason</span>
                  <p className="col-span-2 text-red-600">{selectedRequest.rejection_reason}</p>
                </div>}
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Rejection Dialog */}
        <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-red-600">
                <X className="h-5 w-5" />
                Reject Leave Request
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="text-sm text-gray-600">
                Please provide a reason for rejecting this leave request:
              </div>
              <Textarea
                placeholder="Enter rejection reason..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setRejectDialogOpen(false);
                  setRejectionReason('');
                  setSelectedRequest(null);
                }}
              >
                Cancel
              </Button>
              <Button 
                type="button" 
                variant="destructive"
                onClick={handleRejectConfirm}
                disabled={!rejectionReason.trim()}
              >
                Reject Request
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  )
}


