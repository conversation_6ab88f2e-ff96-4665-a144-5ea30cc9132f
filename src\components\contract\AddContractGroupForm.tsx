import { useState } from "react";
import { User, Calendar, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

// UI Components
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";

// Types
interface Employee {
  id: string;
  employee_code: string;
  user_id: string;
  user_profiles: {
    first_name: string;
    last_name: string;
  };
}

interface AddContractGroupFormProps {
  employees: Employee[];
  onSuccess: () => void;
  onCancel: () => void;
}

export function AddContractGroupForm({ employees, onSuccess, onCancel }: AddContractGroupFormProps) {
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [formLoading, setFormLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    employee_id: "",
    status: "active"
  });

  const { toast } = useToast();

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      employee_id: "",
      status: "active"
    });
    setStartDate(undefined);
    setEndDate(undefined);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.employee_id || !startDate) {
      toast({
        title: "Missing Information",
        description: "Please fill out all required fields.",
        variant: "destructive",
      });
      return;
    }

    setFormLoading(true);
    try {
      const { error } = await supabase
        .from('contract_groups')
        .insert({
          name: formData.name,
          description: formData.description,
          employee_id: formData.employee_id,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate ? endDate.toISOString().split('T')[0] : null,
          status: formData.status
        } as any);

      if (error) throw error;

      toast({
        title: "Success!",
        description: "The contract group has been created successfully.",
      });
      resetForm();
      onSuccess();
    } catch (error) {
      console.error('Error creating contract group:', error);
      toast({
        title: "Creation Failed",
        description: "There was a problem creating the contract group.",
        variant: "destructive",
      });
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="md:col-span-2">
          <Label htmlFor="name">Group Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="e.g., Senior Developer Agreement 2025"
            required
            className="mt-1"
          />
        </div>
        <div className="md:col-span-2">
          <Label htmlFor="employee">Employee *</Label>
          <Select 
            value={formData.employee_id} 
            onValueChange={(value) => setFormData({ ...formData, employee_id: value })} 
            required
          >
            <SelectTrigger id="employee" className="mt-1">
              <SelectValue placeholder="Select an employee" />
            </SelectTrigger>
            <SelectContent>
              {employees.map((employee) => (
                <SelectItem key={employee.id} value={employee.id}>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span>{`${employee.user_profiles.first_name} ${employee.user_profiles.last_name} (${employee.employee_code})`}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Start Date *</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-full justify-start text-left font-normal mt-1", !startDate && "text-muted-foreground")}
              >
                <Calendar className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>
        <div>
          <Label>End Date (Optional)</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-full justify-start text-left font-normal mt-1", !endDate && "text-muted-foreground")}
              >
                <Calendar className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent 
                mode="single" 
                selected={endDate} 
                onSelect={setEndDate} 
                disabled={{ before: startDate }} 
                initialFocus 
              />
            </PopoverContent>
          </Popover>
        </div>
        <div>
          <Label htmlFor="status">Status</Label>
          <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
            <SelectTrigger id="status" className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="terminated">Terminated</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="md:col-span-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Add any notes or details about this contract group..."
            className="mt-1"
          />
        </div>
      </div>
      <div className="flex items-center justify-end gap-4 pt-4">
        <Button type="button" variant="ghost" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={formLoading}>
          {formLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : "Create Contract Group"}
        </Button>
      </div>
    </form>
  );
}